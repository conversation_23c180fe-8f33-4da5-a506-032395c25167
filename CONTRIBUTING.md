# Welcome to developer Contribution guide

Please start with the [**README.MD**](https://github.com/livelearninnovate/foundation/blob/develop/README.md) file to go through the project setup, basic tips and tricks and commands that you can use. Also note the [client side repository ](https://github.com/livelearninnovate/client-apps) that you can use with the backend to run the whole application locally.

As usually, stick to the KISS rule (keep it simple & stupid)

## Table of contents

- [Commit and commit messages structure](#commit-and-commit-messages-structure)
- [Review process](#review-process)
- [Miscellanious](#miscellanious)
  - [Secrets](#secrets)
  - [Units of measurement](#units-of-measurement)

## Commit and commit messages structure

    feat:     The new feature being added to a particular application
    fix:      A bug fix
    style:    Feature and updates related to styling
    refactor: Refactoring a specific section of the codebase
    test:     Everything related to testing
    docs:     Everything related to documentation
    chore:    Regular code maintenance
    release:  Code releases

## Gitflow

For delivering features:

- Start from `develop` branch, create the branch from ClickUp linked to concrete task you are working on and assigned to
- Use commits as delivering units of work, prefixed with type of commit (see [here](#commit-and-commit-messages-structure))
- When feature is ready, <PERSON>reate pull request from ClickUp from the ticket you are working on
- Assign yourself to the pull request
- `Validate checks and tests` are passing
- `Assign labels` to the PR, marking the meaning of the delivered changes
- If the PR making graphical changes on the client side, assign UX specialist for the review as well
- `Squash merge to develop` when pull request is approved

Branch name should match the `[branch_type]/[name]/[task_name]` template.
Pull request name should match the `[main_commit_type]: [task_name]`

## Review process

Before asking for review:

- Watch out for workflows, git hooks and `validate checks and tests` are passing
- Test that app is working and the feature is working
- Remove dead code - prints, temporary namings, comments which should not be there, empty imports

When reviewing, ideally:

- Pull the actual code and try to run the application
- Read through the code with empasis on quality
- Alwyas `prioritize reviewing other tasks so they are not blocked`, communicate any time you need review or re-review, or when you are finished reviewing.

## Miscellanious

### Secrets

`Always keep secrets out of the repository!`

There is an `.env.local` file intended to be used for local development. If you add any new secret to the application, add it to `.env.local.sample` file so others can use it as well. Periodically check `.env.local.sample` for any new secrets and add those to your own `.env.local` (it is in gitgnore).

Use Github secrets or AWS Secrets manager for all secrets, api keys.

### Units of measurement

Store all measurements in `International System of Units` and only present them based on user settings
