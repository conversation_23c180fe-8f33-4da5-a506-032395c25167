# syntax = docker/dockerfile:1.2
FROM python:3.13-slim-bookworm AS master_base

RUN rm -f /etc/apt/apt.conf.d/docker-clean
RUN --mount=type=cache,sharing=locked,target=/var/cache/apt \
    --mount=type=cache,sharing=locked,target=/var/lib/apt \
    apt-get update && \
    apt-get dist-upgrade -yq && \
    apt-get install -yq \
    libpq-dev \
    python3-dev \
    build-essential \
    curl \
    tidy
RUN rm -rf /var/lib/apt/lists/*

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONPATH /app_code
ENV IS_CONTAINERIZED true
ENV APP_VERSION $APP_VERSION
ENV BUILD_VERSION $BUILD_VERSION

RUN --mount=type=cache,target=/root/.cache/pip pip install --upgrade uv
COPY ./requirements-dev.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements-dev.txt
COPY ./services/base/requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt


WORKDIR /app_code
COPY . .

# Remote stage creates user with limited permissions
FROM master_base AS server

RUN groupadd -g 9995 appuser && \
    useradd -mu 1000 -g appuser appuser

RUN chown -R appuser: /app_code
USER appuser

# Development stage
FROM master_base AS local
