# Foundation
The whole backend codebase for the [**Best Life**](https://www.llif.org/our-software) application.

##  Table of contents
- [Directory structure](#directory-structure)
- [Launching the environment](#launching-the-environment)
  - [Windows](#windows)
  - [Linux](#linux)
  - [MacOS](#macos)
- [Bringing up the stack](#bringing-up-the-stack)
  - [Setting up lambdas](#setting-up-lambdas)
- [Stack components](#stack-components)
- [Uploading file data](#uploading-file-data)
  - [Loading your data](#loading-your-data)
  - [Sample data folders](#sample-data-folders)
  - [Loading Weather, Air Quality, Pollen cache](#loading-weather-air-quality-pollen-cache)
- [Working with databases](#working-with-databases)
  - [Using pgAdmin](#using-pgadmin)
  - [Migrating SQLAlchemy through Alembic](#migrating-sqlalchemy-through-alembic)
- [Tearing down the stack](#tearing-down-the-stack)

##  Directory Structure
    .
    ├── golang/wxcache                # API for all stuff related to the weather and air quality
    ├── infrastructure                # Defines the infrastructure and deployment scripts
    ├── seed_data                     # Demo data used for testing purposes
    └── services                      # Code for the application's services
        ├── base                      # The core module for common functionality
        ├── auth_service              # Handles authorization code flow (CURRENTLY UNUSED)
        ├── data_service              # Handles data querying from the client
        ├── file_service              # Handles file uploads and loading into OpenSearch
        ├── mobile_service            # Handles data upload, AHK + Diary
        ├── nginx                     # Internal routing when ran in Docker
        ├── user_service              # Handles all user specific endpoints
        └── serverless                # Code for all of the lambdas
            ├── alexa_skill_life_log  # A lambda which handles the Alexa skill app
            ├── apps                  # Internal lambdas written by us
    ├── settings                      # .ENV files, config, and secrets
    └── tests                         # testing files
---
# Launching the environment
Before you continue with the codebase setup, follow one of the platform specific sections for setting up your development environment. [Windows](#windows) / [Linux](#linux) / [MacOS](#macos). 

After your environment is ready, set up an SSH access to your GitHub account. How to [here](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent). After you are done, add your public key to your GitHub account.

You can check that you've authorized correctly by running:
```bash
> ssh -T **************
# Output: Hi [your_username]! You've successfully authenticated, but GitHub does not provide shell access.
```

Now you are ready to set up the repository!


```bash
  # clone the repo
  > git clone https://github.com/livelearninnovate/foundation.git

  # set up the dotenv file
  > cp settings/.env.local.sample settings/.env.local
```

There are 3 env vars with the `"secret"` value. These have to be filled in manually.
```
AMAZON_OAUTH2_WEB_CLIENT_SECRET=secret
GOOGLE_OAUTH2_SECRET=secret
LOCALSTACK_API_KEY=secret
```

If you completed everything above, move to the [bringing up the stack](#bringing-up-the-stack) section.

## MacOS
First you need to [download Docker](https://docs.docker.com/desktop/mac/install/). 
Then adjust the memory limitation in the Docker Desktop GUI. Go to `Preferences -> Resources -> Advanced` and then set `Memory` to 4 GB or higher.

## Linux
```bash
  # Update the local packages
  > sudo apt update

  # Install docker, docker-compose, and unzip
  > sudo apt install -y docker docker-compose unzip python3-pip curl

  # Add your user to the docker group (requires relog)
  > sudo adduser %username% docker

  # Tune the maximum memory allocation
  > sudo sysctl -w vm.max_map_count=262144
```
More information [here](https://docs.docker.com/engine/install/ubuntu/). For distributions other than Ubuntu [go here](https://docs.docker.com/engine/install/#server).

## Windows
The recommended setup for running this repository is through [WSL](https://docs.microsoft.com/en-us/windows/wsl/install#prerequisites) with an **Ubuntu** OS. While installing WSL, choose the `Ubuntu 20.04 LTS` installation. 

Make sure you are using **WSL 2**. More info [here](https://docs.microsoft.com/en-us/windows/wsl/install-win10#set-your-distribution-version-to-wsl-1-or-wsl-2).

Optionally, If you have any issues with the WSL eating up your resources, you can specify a `.wslconfig` file in your `C:\Users\<USER>