from datetime import datetime
from sys import argv


def bump_version_name(previous_version: str):
    year, week, version = previous_version.split(sep=".")
    version = int(version)
    # Format of YYYY.WW
    now = datetime.now().strftime("%Y.%V")
    if now == f"{year}.{week}":
        version = version + 1
        new_version = f"{year}.{week}.{version}"
    else:
        new_version = f"{now}.1"
    return new_version


if __name__ == "__main__":
    previous_version = argv[1]
    print(bump_version_name(previous_version=previous_version))
