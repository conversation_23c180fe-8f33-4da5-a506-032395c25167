services:
  file_service:
    image: file_service
    container_name: file_service
    restart: on-failure
    build:
      context: .
      dockerfile: ./services/file_service/Dockerfile
      target: local
    command: uvicorn services.file_service.main:app --reload --timeout-graceful-shutdown 1 --host 0.0.0.0 --port 8001
    environment:
      - PYTHONPATH=/app_code/
      - APP_VERSION
      - BUILD_VERSION
    volumes:
      - .:/app_code
    ports:
      - "8001:8001"
    networks:
      stack:
        aliases:
          - file_service
    env_file:
      - ./settings/.env.${RUN_ENV:-local}

  data_service:
    image: data_service
    container_name: data_service
    restart: on-failure
    build:
      context: .
      dockerfile: ./services/data_service/Dockerfile
      target: local
    command: uvicorn services.data_service.main:app --reload --timeout-graceful-shutdown 1 --host 0.0.0.0 --port 8003
    environment:
      - PYTHONPATH=/app_code/
      - APP_VERSION
      - BUILD_VERSION
    volumes:
      - .:/app_code
    ports:
      - "8003:8003"
    networks:
      stack:
        aliases:
          - data_service
    env_file:
      - ./settings/.env.${RUN_ENV:-local}

  user_service:
    image: user_service
    container_name: user_service
    restart: on-failure
    build:
      context: .
      dockerfile: ./services/user_service/Dockerfile
      target: local
    command: >
      sh -c
          "cd ./services/base/infrastructure/database/sql_alchemy/migrations &&
          alembic upgrade head &&
          cd ../../../../../.. &&
          python3 -m services.base.infrastructure.database.sql_alchemy.run_seeders &&
          uvicorn services.user_service.main:app --reload --timeout-graceful-shutdown 1 --host 0.0.0.0 --port 8004"
    environment:
      - PYTHONPATH=/app_code/
      - APP_VERSION
      - BUILD_VERSION
    volumes:
      - .:/app_code
    ports:
      - "8004:8004"
    networks:
      stack:
        aliases:
          - user_service
    env_file:
      - ./settings/.env.${RUN_ENV:-local}

  mobile_service:
    image: mobile_service
    container_name: mobile_service
    restart: on-failure
    build:
      context: .
      dockerfile: ./services/mobile_service/Dockerfile
      target: local
    command: uvicorn services.mobile_service.main:app --reload --timeout-graceful-shutdown 1 --host 0.0.0.0 --port 8005
    environment:
      - PYTHONPATH=/app_code/
      - APP_VERSION
      - BUILD_VERSION
    volumes:
      - .:/app_code
    ports:
      - "8005:8005"
    networks:
      stack:
        aliases:
          - mobile_service
    env_file:
      - ./settings/.env.${RUN_ENV:-local}

  wxcache:
    image: wxcache
    container_name: wxcache
    restart: on-failure
    build:
      context: ./golang/wxcache
    environment:
      MOCK_HTTP: "false"
      CACHE_SIZE: 10000
      BACKFILL: "false"
      INCLUDE_CACHE: "false"
      APP_VERSION:
      BUILD_VERSION:
    ports:
      - "8006:8006"
    networks:
      stack:
        aliases:
          - wxcache
    env_file:
      - ./settings/.env.${RUN_ENV:-local}

  auth_service:
    image: auth_service
    container_name: auth_service
    restart: on-failure
    profiles:
      - disabled
    build:
      context: .
      dockerfile: ./services/auth_service/Dockerfile
    command: uvicorn services.auth_service.main:app --reload --timeout-graceful-shutdown 1 --host 0.0.0.0 --port 8007
    environment:
      - PYTHONPATH=/app_code/
      - APP_VERSION
      - BUILD_VERSION
    volumes:
      - .:/app_code
    ports:
      - 8007:8007
    networks:
      stack:
        aliases:
          - auth_service
    env_file:
      - ./settings/.env.${RUN_ENV:-local}

volumes:
  file_service:
    driver: local
  data_service:
    driver: local
  user_service:
    driver: local
  mobile_service:
    driver: local
  wxcache:
    driver: local
  auth_service:
    driver: local

networks:
  stack:
    name: stack
    external: true
