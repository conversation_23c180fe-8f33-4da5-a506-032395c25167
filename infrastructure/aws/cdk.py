#!/usr/bin/env python3

import aws_cdk as cdk
from aws_cdk import Environment

from infrastructure.aws.cdk.messaging_stack import MessagingStack
from settings.app_config import settings

app = cdk.App()
messaging_stack = MessagingStack(
    scope=app,
    id=f"{settings.RUN_ENV}-messaging-stack",
    env=Environment(region=settings.AWS_REGION_NAME, account=settings.AWS_ACCOUNT),
)
cdk.Tags.of(messaging_stack).add("env", settings.RUN_ENV)
app.synth()
