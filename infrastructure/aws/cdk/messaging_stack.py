from aws_cdk import Duration, Environment, Stack
from aws_cdk.aws_sns import FilterOrPolicy, SubscriptionFilter, Topic
from aws_cdk.aws_sns_subscriptions import SqsSubscription
from aws_cdk.aws_sqs import DeadLetterQueue, Queue
from constructs import Construct

from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.constants.messaging import MessageQueuesNames, MessageTopics
from services.base.domain.constants.time_constants import SECONDS_IN_14_DAYS
from settings.app_config import settings


class MessagingStack(Stack):

    def __init__(self, scope: Construct, id: str, env: Environment, **kwargs) -> None:
        super().__init__(scope=scope, id=id, env=env, **kwargs)
        # Dead letter queue
        error_queue = Queue(
            self,
            id=MessageQueuesNames.ERROR_QUEUE.value,
            queue_name=MessageQueuesNames.ERROR_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        # Environment queues
        air_quality_queue = Queue(
            self,
            id=MessageQueuesNames.AIR_QUALITY_QUEUE.value,
            queue_name=MessageQueuesNames.AIR_QUALITY_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        weather_queue = Queue(
            self,
            id=MessageQueuesNames.WEATHER_QUEUE.value,
            queue_name=MessageQueuesNames.WEATHER_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        pollen_queue = Queue(
            self,
            id=MessageQueuesNames.POLLEN_QUEUE.value,
            queue_name=MessageQueuesNames.POLLEN_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        # Extensions queues
        trend_insights_job_queue = Queue(
            self,
            id=MessageQueuesNames.TREND_INSIGHTS_JOB_QUEUE.value,
            queue_name=MessageQueuesNames.TREND_INSIGHTS_JOB_QUEUE.value,
            visibility_timeout=Duration.seconds(900),
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
            dead_letter_queue=DeadLetterQueue(queue=error_queue, max_receive_count=5),
        )

        single_correlation_job_queue = Queue(
            self,
            id=MessageQueuesNames.SINGLE_CORRELATION_JOB_QUEUE.value,
            queue_name=MessageQueuesNames.SINGLE_CORRELATION_JOB_QUEUE.value,
            visibility_timeout=Duration.seconds(900),
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
            dead_letter_queue=DeadLetterQueue(queue=error_queue, max_receive_count=5),
        )

        notify_handler_job_queue = Queue(
            self,
            id=MessageQueuesNames.NOTIFY_HANDLER_JOB.value,
            queue_name=MessageQueuesNames.NOTIFY_HANDLER_JOB.value,
            visibility_timeout=Duration.seconds(900),
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
            dead_letter_queue=DeadLetterQueue(queue=error_queue, max_receive_count=5),
        )

        # Service queues (assuming default settings for these)
        file_events_base_queue = Queue(
            self,
            id=MessageQueuesNames.FILE_EVENTS_BASE_QUEUE.value,
            queue_name=MessageQueuesNames.FILE_EVENTS_BASE_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        _ = Queue(
            self,
            id=MessageQueuesNames.FILE_EVENTS_ERROR_QUEUE.value,
            queue_name=MessageQueuesNames.FILE_EVENTS_ERROR_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        user_events_base_queue = Queue(
            self,
            id=MessageQueuesNames.USER_EVENTS_BASE_QUEUE.value,
            queue_name=MessageQueuesNames.USER_EVENTS_BASE_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        _ = Queue(
            self,
            id=MessageQueuesNames.USER_EVENTS_ERROR_QUEUE.value,
            queue_name=MessageQueuesNames.USER_EVENTS_ERROR_QUEUE.value,
            retention_period=Duration.seconds(SECONDS_IN_14_DAYS),
        )

        # Topics
        upload_finished_topic = Topic(
            self,
            id=MessageTopics.TOPIC_UPLOAD_FINISHED.value,
            topic_name=MessageTopics.TOPIC_UPLOAD_FINISHED.value,
        )
        upload_finished_topic.add_subscription(topic_subscription=SqsSubscription(queue=file_events_base_queue))

        extract_finished_topic = Topic(
            self,
            id=MessageTopics.TOPIC_EXTRACT_FINISHED.value,
            topic_name=MessageTopics.TOPIC_EXTRACT_FINISHED.value,
        )
        extract_finished_topic.add_subscription(topic_subscription=SqsSubscription(queue=file_events_base_queue))

        load_finished_topic = Topic(
            self, id=MessageTopics.TOPIC_LOAD_FINISHED.value, topic_name=MessageTopics.TOPIC_LOAD_FINISHED.value
        )
        load_finished_topic.add_subscription(topic_subscription=SqsSubscription(queue=file_events_base_queue))

        takeout_export_scheduled_topic = Topic(
            self,
            id=MessageTopics.TOPIC_TAKEOUT_EXPORT_SCHEDULED.value,
            topic_name=MessageTopics.TOPIC_TAKEOUT_EXPORT_SCHEDULED.value,
        )
        takeout_export_scheduled_topic.add_subscription(
            topic_subscription=SqsSubscription(queue=file_events_base_queue)
        )

        location_loading_finished_topic = Topic(
            self,
            id=MessageTopics.TOPIC_LOCATION_LOADING_FINISHED.value,
            topic_name=MessageTopics.TOPIC_LOCATION_LOADING_FINISHED.value,
        )
        location_loading_finished_topic.add_subscription(topic_subscription=SqsSubscription(queue=air_quality_queue))
        location_loading_finished_topic.add_subscription(topic_subscription=SqsSubscription(queue=weather_queue))
        location_loading_finished_topic.add_subscription(topic_subscription=SqsSubscription(queue=pollen_queue))

        analytic_scheduled_topic = Topic(
            self,
            id=MessageTopics.TOPIC_ANALYTIC_SCHEDULED.value,
            topic_name=MessageTopics.TOPIC_ANALYTIC_SCHEDULED.value,
        )
        analytic_scheduled_topic.add_subscription(
            topic_subscription=SqsSubscription(
                queue=trend_insights_job_queue,
                filter_policy_with_message_body={
                    ExtensionLabels.EXTENSION_ID: FilterOrPolicy.filter(
                        SubscriptionFilter.string_filter(allowlist=[str(settings.TREND_INSIGHTS_EXTENSION_ID)])
                    )
                },
            )
        )
        analytic_scheduled_topic.add_subscription(
            topic_subscription=SqsSubscription(
                queue=single_correlation_job_queue,
                filter_policy_with_message_body={
                    ExtensionLabels.EXTENSION_ID: FilterOrPolicy.filter(
                        SubscriptionFilter.string_filter(
                            allowlist=[str(settings.SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID)]
                        )
                    )
                },
            )
        )

        analytic_finished_topic = Topic(
            self,
            id=MessageTopics.TOPIC_ANALYTIC_FINISHED.value,
            topic_name=MessageTopics.TOPIC_ANALYTIC_FINISHED.value,
        )
        analytic_finished_topic.add_subscription(topic_subscription=SqsSubscription(queue=notify_handler_job_queue))

        member_user_registered_topic = Topic(
            self,
            id=MessageTopics.TOPIC_MEMBER_USER_REGISTERED.value,
            topic_name=MessageTopics.TOPIC_MEMBER_USER_REGISTERED.value,
        )
        member_user_registered_topic.add_subscription(topic_subscription=SqsSubscription(queue=user_events_base_queue))

        member_user_account_linked_topic = Topic(
            self,
            id=MessageTopics.TOPIC_MEMBER_USER_ACCOUNT_LINKED.value,
            topic_name=MessageTopics.TOPIC_MEMBER_USER_ACCOUNT_LINKED.value,
        )
        member_user_account_linked_topic.add_subscription(
            topic_subscription=SqsSubscription(queue=notify_handler_job_queue)
        )
        takeout_export_finished_topic = Topic(
            self,
            id=MessageTopics.TOPIC_TAKEOUT_EXPORT_FINISHED.value,
            topic_name=MessageTopics.TOPIC_TAKEOUT_EXPORT_FINISHED.value,
        )
        takeout_export_finished_topic.add_subscription(
            topic_subscription=SqsSubscription(queue=notify_handler_job_queue)
        )
