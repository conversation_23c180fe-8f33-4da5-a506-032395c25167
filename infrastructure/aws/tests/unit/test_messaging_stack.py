import pytest
from aws_cdk import App, Environment
from aws_cdk.assertions import Template

from infrastructure.aws.cdk.messaging_stack import MessagingStack
from services.base.domain.constants.messaging import MessageQueuesNames, MessageTopics
from settings.app_config import settings


@pytest.fixture
def stack_template() -> Template:
    app = App()
    stack = MessagingStack(
        app, f"{settings.RUN_ENV}-messaging-stack", env=Environment(account="************", region="us-east-1")
    )
    return Template.from_stack(stack)


def test_sqs_queue_created(stack_template):
    for queue in MessageQueuesNames:
        stack_template.has_resource_properties("AWS::SQS::Queue", {"QueueName": queue.value})


def test_sns_topic_created(stack_template):
    for topic in MessageTopics:
        stack_template.has_resource_properties("AWS::SNS::Topic", {"TopicName": topic.value})
