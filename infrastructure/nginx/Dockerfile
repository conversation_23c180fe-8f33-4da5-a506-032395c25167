FROM nginx:1.27-bookworm
R<PERSON> apt-get update && apt-get install -y cron && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled
COPY infrastructure/nginx/nginx.conf /etc/nginx/nginx.conf
COPY infrastructure/nginx/sites-available/live.conf.template /etc/nginx/sites-available/live.conf.template
COPY infrastructure/nginx/sites-available/maintenance.conf.template /etc/nginx/sites-available/maintenance.conf.template
COPY infrastructure/nginx/entrypoint.sh /entrypoint.sh
COPY infrastructure/nginx/maintenance.sh /maintenance.sh

# Create a symbolic link for live mode in sites-enabled - the file is populated bt the entrypoint.sh
RUN ln -s /etc/nginx/sites-available/live.conf /etc/nginx/sites-enabled/default.conf

# Set up cron job to reload Nginx at midnight to pick up new certificates
RUN echo "0 0 * * * nginx -s reload" > /etc/cron.d/nginx-reload && chmod 0644 /etc/cron.d/nginx-reload

EXPOSE 443

# Make scripts executable
RUN chmod +x /entrypoint.sh

CMD ["/entrypoint.sh"]
