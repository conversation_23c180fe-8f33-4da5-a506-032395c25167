# Nginx Mode Switching with Symbolic Links

This readme explains how to switch between **live** and **maintenance** nginx modes by managing symbolic links.

---

## **Directory Structure**

```plaintext
nginx/
├── Dockerfile             # Built
├── nginx.conf             #
├── maintenance.sh         #
├── sites-available/       # Predefined sites configuration
│   ├── live.conf          # Configuration for live mode
│   ├── maintenance.conf   # Configuration for maintenance mode
├── sites-enabled/         # Active sites configuration
│   ├── default.conf       # -> Symlink to sites-available
```

## **Use maintenance.sh**

```bash
# connect to the nginx container
docker exec -it nginx bash

cd /etc/nginx/sites-available

# Turn maintenance on
./maintenance.sh on

# Turn maintenance off
./maintenance.sh off
```

## **Switch to Maintenance Mode**

```bash
# Remove the current symbolic link
rm /etc/nginx/sites-enabled/default.conf

# Create a new symbolic link to maintenance.conf
ln -s /etc/nginx/sites-available/maintenance.conf /etc/nginx/sites-enabled/default.conf
```

## **Switch to Live Mode**

```bash
# Remove the current symbolic link
rm /etc/nginx/sites-enabled/default.conf

# Create a new symbolic link to live.conf
ln -s /etc/nginx/sites-available/live.conf /etc/nginx/sites-enabled/default.conf
```

## **Test the configuration **

```bash
# Test the Nginx configuration
nginx -t

# Reload Nginx to apply changes
nginx -s reload

# Test connection -> should return 502 if life, 503 if maitenance
curl -I https://api.my.llif.org
```
