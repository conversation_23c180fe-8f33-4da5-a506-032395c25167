#!/bin/sh

# Substitute environment variables in configuration templates
envsubst '${API_DOMAIN}' < /etc/nginx/sites-available/live.conf.template > /etc/nginx/sites-available/live.conf
envsubst '${API_DOMAIN}' < /etc/nginx/sites-available/maintenance.conf.template > /etc/nginx/sites-available/maintenance.conf

# Start cron service in background
cron

# Start NGINX in foreground
exec nginx -g "daemon off;"
