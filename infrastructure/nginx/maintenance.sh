#!/bin/bash

NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
DEFAULT_CONF="default.conf"

if [ $# -ne 1 ]; then
    echo "Usage: $0 on|off"
    exit 1
fi

switch_mode() {
    local mode=$1
    local target_conf

    if [ "$mode" = "on" ]; then
        target_conf="maintenance.conf"
        echo "Nginx maintenance ON"
    elif [ "$mode" = "off" ]; then
        target_conf="live.conf"
        echo "Nginx maintenance OFF"
    else
        echo "Invalid mode: $mode"
        exit 1
    fi

    # Remove existing symlink and create a new one
    rm -f "$NGINX_SITES_ENABLED/$DEFAULT_CONF"
    ln -s "$NGINX_SITES_AVAILABLE/$target_conf" "$NGINX_SITES_ENABLED/$DEFAULT_CONF"
    # Reload Nginx
    nginx -s reload
}

switch_mode "$1"
