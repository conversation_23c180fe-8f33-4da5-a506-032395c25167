env API_DOMAIN;

worker_processes auto;

events {
    worker_connections 1024;
    multi_accept on;
}

http {
    # Includes
    include /etc/nginx/sites-enabled/*;
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # GZIP
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml+rss text/javascript;
    gzip_comp_level 6;
    gzip_min_length 256;

    # Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    server_tokens off;
    # server_names_hash_bucket_size 64;
    # server_name_in_redirect off;

    # SSL Settings
    ssl_protocols TLSv1.3;
    ssl_prefer_server_ciphers off;
    add_header Strict-Transport-Security "max-age=63072000" always;

    # Additional directives
}
