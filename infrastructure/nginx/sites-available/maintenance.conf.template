server {
    server_name $API_DOMAIN;

    listen 443 ssl;
    http2 on;
    listen 443 quic reuseport;

    location / {
        add_header Retry-After 3600;
        return 503 "Service temporarily unavailable due to maintenance.";
    }

    ssl_certificate /etc/letsencrypt/live/$API_DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$API_DOMAIN/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}
