.DEFAULT_GOAL := help
.EXPORT_ALL_VARIABLES:
COVERAGE := 80
DOCKER_BUILDKIT := 1
COMPOSE_DOCKER_CLI_BUILD := 1
RUN_ENV := production

clean: ## Cleans all project docker images and removes containers
	docker compose down --remove-orphans -v --rmi all

down: ## Stops and removes project docker containers
	docker compose down --remove-orphans

deploy_lambdas: ## Deploy serverless lambdas
	cd services/serverless/apps && make build
	cd services/serverless/apps && NODE_ENV=$(RUN_ENV) serverless deploy --stage=$(RUN_ENV)

get_os_schema: ## Runs the get_schema script to export current OpenSearch schema
	echo "Running get_schema"
	python3 -m services.base.infrastructure.database.opensearch.get_schema

help: ## Show this help (runs only in bash)
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

launch: ## Launches whole project in docker
	docker compose up -d

load_samples: ## Loads development samples into OpenSearch database
	docker run -e RUN_ENV=$(RUN_ENV) --rm file_service python3 -m services.file_service.application.loaders.run_loaders

migrate: ## Migrates the SQL databases
	docker run -e RUN_ENV=$(RUN_ENV) --rm user_service bash -c "cd ./services/base/infrastructure/database/sql_alchemy/migrations && alembic upgrade head"
	docker run -e RUN_ENV=$(RUN_ENV) --rm user_service python3 -m services.base.infrastructure.database.sql_alchemy.run_seeders

rebuild: ## Rebuilds all project dockerfiles
	docker compose build

restart: ## Restarts api services
	docker compose restart

provision_infrastructure: ## sets up infrastructure resources
	docker build -f ./infrastructure/aws/Dockerfile -t aws_provisioner .
	docker run --rm --env-file ./settings/.env.${RUN_ENV} aws_provisioner \
		bash -c "cdk bootstrap --app=infrastructure/aws/cdk.py && \
		cdk deploy --app=infrastructure/aws/cdk.py --require-approval=never --method=direct --force"

snapshot: ## Creates opensearch snapshot at current time
	docker run --rm -e RUN_ENV=$(RUN_ENV) data_service bash -lce "pip3 install typer requests_aws4auth && python3 -m infrastructure.scripts.opensearch_snapshots create"

test: rebuild ## Runs tests in services (specific for each service)
	make --file=./services/data_service/Makefile test_data_service
	make --file=./services/file_service/Makefile test_file_service
	make --file=./services/mobile_service/Makefile test_mobile_service
	make --file=./services/user_service/Makefile test_user_service
# 	cd services/serverless && make test
