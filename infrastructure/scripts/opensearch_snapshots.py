import logging
from datetime import datetime, timezone
from typing import Optional

import boto3
import requests
import typer
from pydantic import Field
from requests_aws4auth import <PERSON><PERSON>4<PERSON><PERSON>

from services.base.domain.schemas.shared import BaseDataModel
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from settings.app_config import settings
from settings.app_secrets import secrets

logger = logging.getLogger()
logger.setLevel(logging.INFO)

credentials = boto3.Session().get_credentials()
awsauth = AWS4Auth(
    credentials.access_key, credentials.secret_key, settings.AWS_REGION_NAME, "es", session_token=credentials.token
)


app = typer.Typer()
snapshot_url = settings.OS_HOSTS[0] + "/_snapshot"


class OpenSearchSnapshotRepository(BaseDataModel):
    name: str
    type: str
    settings: Optional[dict[str, str]] = None


@app.command("list_repositories")
def list_repositories():
    class ListRepositoriesOutputBoundary(BaseDataModel):
        repositories: list[OpenSearchSnapshotRepository]

    url = snapshot_url + "/_all?pretty"
    headers = {"Content-Type": "application/json"}

    # Create repository if it does not exist yet
    response = requests.get(url, auth=awsauth, headers=headers)
    output = ListRepositoriesOutputBoundary(
        repositories=[OpenSearchSnapshotRepository(name=key, **value) for key, value in response.json().items()]
    )

    logger.info(
        "Existing snapshot repositories:\n", *[repository.name for repository in output.repositories], sep="\n\t"
    )

    """Example response:
    {
        "cs-automated": {
            "type": "s3"
        },
        "backups": {
            "type": "s3",
            "settings": {
                "bucket": "staging-os-backup",
                "base_path": "os_snapshots",
                "region": "us-east-1",
                "role_arn": "arn:aws:iam::575851995188:role/OSBackupRole"
            }
        }
    }
    """


class OpenSearchSnapshot(BaseDataModel):
    name: str = Field(alias="snapshot")
    start_time: datetime
    state: str
    failures: list[str]


@app.command("list_snapshots")
def list_snapshots(
    repository_name: str = "backups",
):
    class ListSnapshotsOutputBoundary(BaseDataModel):
        snapshots: list[OpenSearchSnapshot]

    url = snapshot_url + f"/{repository_name}/*?pretty"
    headers = {"Content-Type": "application/json"}

    # Create repository if it does not exist yet
    response = requests.get(url, auth=awsauth, headers=headers)
    snapshots: list[dict] = response.json().get("snapshots")
    output = ListSnapshotsOutputBoundary(snapshots=[OpenSearchSnapshot(**snapshot) for snapshot in snapshots])
    output.snapshots.sort(key=lambda s: s.start_time)

    snapshot_lines = "\n\t" + "\n\t".join(f"{snapshot.name} | {snapshot.state}" for snapshot in output.snapshots)
    logger.info(f"Existing snapshots for repository {repository_name}:{snapshot_lines}")

    """example response: 
    {
      "snapshots" : [ {
        "snapshot" : "2023-04-20t14:24:56",
        "uuid" : "EWpDAv8sRaaEvRo7teX8LQ",
        "version_id" : 136267827,
        "version" : "2.5.0",
        "indices" : [ "weather_cache-2022", ...],
        "data_streams" : [ ],
        "include_global_state" : true,
        "state" : "SUCCESS",
        "start_time" : "2023-04-20T14:24:57.548Z",
        "start_time_in_millis" : 1682000697548,
        "end_time" : "2023-04-20T14:26:31.703Z",
        "end_time_in_millis" : 1682000791703,
        "duration_in_millis" : 94155,
        "failures" : [ ],
        "shards" : {
          "total" : 128,
          "failed" : 0,
          "successful" : 128
        }
      } ]
    }"""


@app.command("create")
def create_snapshot(repository_name: str = "backups", snapshot_name: Optional[str] = None):
    """Creates opensearch snapshot at current time and stores is in S3 bucket.
    If no snapshot name is provided, the default is datetime now in %Y-%m-%dt%H:%M format"""
    if not snapshot_name:
        snapshot_name = datetime.now(timezone.utc).strftime("%Y-%m-%dt%H:%M")
    repository_url = snapshot_url + "/" + repository_name

    payload = {
        "type": "s3",
        "settings": {
            "bucket": f"{settings.RUN_ENV}-os-backup",
            "base_path": "os_snapshots",
            "region": settings.AWS_REGION_NAME,
            "role_arn": "arn:aws:iam::575851995188:role/OSBackupRole",
        },
    }

    headers = {"Content-Type": "application/json"}

    # Create repository if it does not exist yet
    response = requests.put(repository_url, auth=awsauth, json=payload, headers=headers)
    logger.info(f"Created snapshot repository {repository_name} with result {response.json()}.")

    # Add snapshot to the repository
    response = requests.put(repository_url + f"/{snapshot_name}", auth=awsauth, json=payload, headers=headers)
    logger.info(f"Created snapshot {snapshot_name} with result {response.json()}.")


if __name__ == "__main__":
    TelemetryInstrumentor.initialize(service_name="snapshot", secrets=secrets, settings=settings)
    app()
