# Welcome to Serverless!
#
# For full config options, check the docs:
#    docs.serverless.com
#

service: llif-apps

# You can pin your service to only deploy with a specific Serverless version
frameworkVersion: "3"

custom:
  stage: "${opt:stage, self:provider.stage}"
  dotenvVars: ${file(parse_env.js)}

provider:
  name: aws
  runtime: python3.10
  tracing:
    lambda: true
  iam:
    role:
      statements:
        - Effect: "Allow"
          Action:
            - "secretsmanager:GetResourcePolicy"
            - "secretsmanager:GetSecretValue"
            - "secretsmanager:DescribeSecret"
            - "secretsmanager:ListSecretVersionIds"
            - "secretsmanager:ListSecrets"
            - "secretsmanager:BatchGetSecretValue"
          Resource:
            - "*" # TODO: this should be limited to just the required secrets
        - Effect: "Allow"
          Action:
            - "sns:Publish"
            - "sns:Subscribe"
            - "sns:Unsubscribe"
            - "sns:ListTopics"
            - "sns:CreateTopic"
            - "sns:TagResource"
          Resource:
            - "*"
        - Effect: "Allow"
          Action:
            - "sqs:ReceiveMessage"
            - "sqs:GetQueueAttributes"
            - "sqs:DeleteMessage"
          Resource:
            - "*"
        - Effect: "Allow"
          Action:
            - "xray:PutTraceSegments"
            - "xray:PutTelemetryRecords"
            - "xray:GetSamplingRules"
            - "xray:GetSamplingTargets"
            - "xray:GetSamplingStatisticSummaries"
          Resource: "*"
  region: us-east-1
  environment: ${file(parse_env.js)}
  vpc:
    securityGroupIds:
      - sg-0ed868dda406e4a2c
    subnetIds:
      - subnet-0ace98be717976e1d
      - subnet-022e8dc4c34e4bde8
      - subnet-0de81f54933b6f0ed

  tags:
    env: ${self:custom.dotenvVars.RUN_ENV}

  ecr:
    images:
      alexa_voice_log:
        path: ./../../../
        file: ./services/serverless/apps/alexa_voice_log/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      analytics_app_scheduler:
        path: ./../../../
        file: ./services/serverless/apps/analytics_scheduler/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      data_consistency_validator:
        path: ./../../../
        file: ./services/serverless/apps/data_consistency_validator/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      notify_handler:
        path: ./../../../
        file: ./services/serverless/apps/notify_handler/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      single_correlation_app:
        path: ./../../../
        file: ./services/serverless/apps/single_correlation_app/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      trend_insights:
        path: ./../../../
        file: ./services/serverless/apps/trend_insights/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}
      usage_statistics_generator:
        path: ./../../../
        file: ./services/serverless/apps/usage_statistics_generator/Dockerfile
        buildArgs:
          RUN_ENV: ${self:custom.dotenvVars.RUN_ENV}

functions:
  AlexaBestLifeEventLog:
    timeout: 300
    image:
      name: alexa_voice_log
    events:
      - alexaSkill: ${self:custom.dotenvVars.ALEXA_VOICE_LOG_ID}

  AnalyticsAppScheduler:
    timeout: 300
    image:
      name: analytics_app_scheduler
    events:
      - schedule: cron(0 * * * ? *) # Every hour

  DataConsistencyValidator:
    timeout: 900
    events:
      - schedule: cron(0 0 * * ? *) # Midnight @ Every day
    image:
      name: data_consistency_validator


  NotifyHandler:
    timeout: 900
    reservedConcurrency: 10
    image:
      name: notify_handler
    events:
      - sqs: arn:aws:sqs:us-east-1:${self:custom.dotenvVars.AWS_ACCOUNT}:${self:custom.dotenvVars.ENVIRONMENT_PREFIX}${self:custom.dotenvVars.QUEUE_NOTIFY_HANDLER_JOB}

  SingleCorrelationApp:
    timeout: 900
    reservedConcurrency: 5
    image:
      name: single_correlation_app
    events:
      - sqs: arn:aws:sqs:us-east-1:${self:custom.dotenvVars.AWS_ACCOUNT}:${self:custom.dotenvVars.ENVIRONMENT_PREFIX}${self:custom.dotenvVars.QUEUE_SINGLE_CORRELATION_JOB}

  TrendInsights:
    timeout: 900
    reservedConcurrency: 5
    image:
      name: trend_insights
    events:
      - sqs: arn:aws:sqs:us-east-1:${self:custom.dotenvVars.AWS_ACCOUNT}:${self:custom.dotenvVars.ENVIRONMENT_PREFIX}${self:custom.dotenvVars.QUEUE_TREND_INSIGHTS_JOB}

  UsageStatisticsGenerator:
    timeout: 300
    image:
      name: usage_statistics_generator
    events:
      - schedule:
          rate: cron(0 0 * * ? *)
          input:
            report_timeframe: "daily"
      - schedule:
          rate: cron(0 0 ? * SUN *)
          input:
            report_timeframe: "weekly"
      - schedule:
          rate: cron(0 0 1 * ? *)
          input:
            report_timeframe: "monthly"
