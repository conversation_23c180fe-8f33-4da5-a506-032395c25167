services:
  file_service:
    image: file_service
    container_name: file_service
    restart: unless-stopped
    build:
      target: server
      context: .
      dockerfile: ./services/file_service/Dockerfile
    command: >
      sh -c
          "./infrastructure/scripts/fuse_s3.sh &&
           uvicorn services.file_service.main:app --host 0.0.0.0 --port 8001"
    environment:
      - PYTHONPATH=/app_code/
      - RUN_ENV=staging
      - APP_VERSION
    volumes:
      - .:/app_code
    privileged: true
    ports:
      - "8001:8001"
    networks:
      services:
        aliases:
          - file_service
    env_file:
      - ./settings/.env.${RUN_ENV:-staging}

  data_service:
    image: data_service
    container_name: data_service
    restart: unless-stopped
    build:
      target: server
      context: .
      dockerfile: ./services/data_service/Dockerfile
    command: uvicorn services.data_service.main:app --host 0.0.0.0 --port 8003
    environment:
      - PYTHONPATH=/app_code/
      - RUN_ENV=staging
      - APP_VERSION
    volumes:
      - .:/app_code
    ports:
      - "8003:8003"
    networks:
      services:
        aliases:
          - data_service
    env_file:
      - ./settings/.env.${RUN_ENV:-staging}

  user_service:
    image: user_service
    container_name: user_service
    restart: unless-stopped
    build:
      target: server
      context: .
      dockerfile: ./services/user_service/Dockerfile
    command: uvicorn services.user_service.main:app --host 0.0.0.0 --port 8004
    environment:
      - PYTHONPATH=/app_code/
      - RUN_ENV=staging
      - APP_VERSION
    volumes:
      - .:/app_code
    ports:
      - "8004:8004"
    networks:
      services:
        aliases:
          - user_service
    env_file:
      - ./settings/.env.${RUN_ENV:-staging}

  mobile_service:
    image: mobile_service
    container_name: mobile_service
    restart: unless-stopped
    build:
      target: server
      context: .
      dockerfile: ./services/mobile_service/Dockerfile
    command: uvicorn services.mobile_service.main:app --host 0.0.0.0 --port 8005
    environment:
      - PYTHONPATH=/app_code/
      - RUN_ENV=staging
      - APP_VERSION
    volumes:
      - .:/app_code
    ports:
      - "8005:8005"
    networks:
      services:
        aliases:
          - mobile_service
    env_file:
      - ./settings/.env.${RUN_ENV:-staging}

  wxcache:
    image: wxcache
    container_name: wxcache
    restart: unless-stopped
    build:
      context: ./golang/wxcache
    environment:
      - RUN_ENV=staging
      - MOCK_HTTP=false
      - CACHE_SIZE=10000
      - APP_VERSION
    ports:
      - "8006:8006"
    networks:
      services:
        aliases:
          - wxcache
    env_file:
      - ./settings/.env.${RUN_ENV:-staging}

  nginx:
    image: nginx
    container_name: nginx
    build:
      context: .
      dockerfile: ./infrastructure/nginx/Dockerfile
    restart: unless-stopped
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt:ro
    ports:
      - "443:443"
    networks:
      services:
        aliases:
          - nginx
    env_file:
      - ./settings/.env.${RUN_ENV:-staging}

volumes:
  file_service:
    driver: local
  data_service:
    driver: local
  user_service:
    driver: local
  mobile_service:
    driver: local
  wxcache:
    driver: local

networks:
  services:
    driver: bridge
