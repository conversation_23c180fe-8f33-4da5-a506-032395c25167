# infrastructure/temporal/temporal_worker/Dockerfile
# syntax = docker/dockerfile:1.2
FROM python:3.13-slim AS temporal_worker

RUN apt-get update && apt-get install -y gcc && apt-get clean

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    APP_VERSION=$APP_VERSION \
    IS_CONTAINERIZED=true \
    BUILD_VERSION=$BUILD_VERSION \
    PYTHONPATH=/app

WORKDIR /app

RUN pip install --upgrade pip && pip install uv

COPY infrastructure/temporal/temporal_worker/requirements.txt .
RUN uv pip install --system -r requirements.txt

COPY services /app/services
COPY settings /app/settings

COPY infrastructure/temporal/temporal_worker/worker.py /app/worker.py

RUN python3 worker.py --check

C<PERSON> ["python3", "worker.py"]