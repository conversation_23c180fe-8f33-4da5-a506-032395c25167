services:
  temporal-worker:
    build:
      context: ../../../
      dockerfile: infrastructure/temporal/temporal_worker/Dockerfile
    environment:
      - TEMPORAL_HOST=temporal
      - TEMPORAL_PORT=7233
      - APP_VERSION=${APP_VERSION:-latest}
      - BUILD_VERSION=${BUILD_VERSION:-latest}
      - RUN_ENV=${RUN_ENV}
    volumes:
      - ../../../services/serverless/apps:/app/services/serverless/apps
    networks:
      - stack

networks:
  stack:
    external: true