import asyncio
import logging
import sys

from temporalio.client import Client
from temporalio.worker import Worker

from services.base.domain.constants.messaging import MessageQueuesNames
from services.serverless.apps.single_correlation_app.handle_single_correlation import handle_single_correlation
from services.serverless.apps.single_correlation_app.single_correlation_workflow import (
    SingleCorrelationWorkflow,
)
from services.serverless.apps.trend_insights.handle_trend_insights import handle_trend_insights
from services.serverless.apps.trend_insights.trend_insights_workflow import TrendInsightsWorkflow
from settings.temporal_config import temporal_settings


async def run_worker():
    client = await Client.connect(f"{temporal_settings.TEMPORAL_HOST}:{temporal_settings.TEMPORAL_PORT}")
    worker = Worker(
        client,
        task_queue=MessageQueuesNames.TEMPORAL_EXTENSION_QUEUE,
        workflows=[SingleCorrelationWorkflow, TrendInsightsWorkflow],
        activities=[handle_single_correlation, handle_trend_insights],
    )
    await worker.run()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    if "--check" in sys.argv:
        logging.info("Worker check passed")
        sys.exit(0)
    asyncio.run(run_worker())
