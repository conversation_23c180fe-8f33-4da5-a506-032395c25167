.DEFAULT_GOAL := help
.EXPORT_ALL_VARIABLES:
COVERAGE := 60
DOCKER_BUILDKIT := 1
DOCKER_NETWORK := stack
COMPOSE_DOCKER_CLI_BUILD := 1
RUN_ENV ?= local
BUILD_VERSION ?= foundation

await_infrastructure: ## Awaits infrastructure to be initialized
	docker run --rm --network $(BUILD_VERSION)-$(DOCKER_NETWORK) appropriate/curl --ipv4 --retry 7 --retry-connrefused http://os01:9200/
	docker run --rm --network $(BUILD_VERSION)-$(DOCKER_NETWORK) appropriate/curl --ipv4 --retry 7 --retry-connrefused http://localstack:4566/
	docker run --rm --network $(BUILD_VERSION)-$(DOCKER_NETWORK) appropriate/curl --ipv4 --retry 7 --retry-connrefused http://azurite:10000/
	docker run --rm --network $(BUILD_VERSION)-$(DOCKER_NETWORK) temporalio/tctl --address temporal:7233 namespace list

clean: ## Cleans all project docker images and removes containers
	docker compose -p $(BUILD_VERSION) down -v --rmi all

deploy_lambdas: ## Deploy serverless lambdas
	cd services/serverless/apps && NODE_ENV=$(RUN_ENV) serverless deploy --stage=$(RUN_ENV)

down: ## Stops and removes project docker containers
	docker compose -p $(BUILD_VERSION) down --remove-orphans

generate_access_token: ## Generates long living access token for local development
	docker exec $(BUILD_VERSION)-data_service python3 -m services.base.api.authentication.generate_access_token

launch: ## Launches whole project in docker
	docker compose -p $(BUILD_VERSION) up -d

load_samples: ## Creates new indices and loads development samples into OpenSearch database
	docker exec $(BUILD_VERSION)-file_service python3 -m services.file_service.application.loaders.run_loaders
	docker exec $(BUILD_VERSION)-mobile_service python3 -m services.mobile_service.application.use_cases.run_loaders

migrate: ## Migrates the SQL databases
	docker exec $(BUILD_VERSION)-user_service bash -c "cd ./services/base/infrastructure/database/sql_alchemy/migrations && alembic upgrade head"
	docker exec $(BUILD_VERSION)-user_service python3 -m services.base.infrastructure.database.sql_alchemy.run_seeders

rebuild: ## Rebuilds all project dockerfiles
	docker compose -p $(BUILD_VERSION) build
	docker build --target wxcache_build_cache -t $(BUILD_VERSION)-wxcache_build_cache ./golang/wxcache

reinitialize_indices: ## Reinitializes all indicis
	docker exec $(BUILD_VERSION)-file_service python3 -m services.base.infrastructure.database.opensearch.opensearch_initializer

provision_infrastructure: ## sets up infrastructure resources
	docker build -f ./infrastructure/aws/Dockerfile -t aws_provisioner .
	docker run --rm --network $(BUILD_VERSION)-$(DOCKER_NETWORK) \
	--env-file ./settings/.env.${RUN_ENV} -e AWS_ACCESS_KEY_ID=fake -e AWS_SECRET_ACCESS_KEY=fake \
		aws_provisioner \
		bash -c "cdklocal bootstrap --app=infrastructure/aws/cdk.py && \
		cdklocal deploy --app=infrastructure/aws/cdk.py --require-approval=never --method=direct"

test_data_service:
	pytest services/base/tests/ --cov=services/base/ --reruns 1 --reruns-delay 5
	pytest services/data_service --cov=services/data_service/ --reruns 1 --reruns-delay 5

test_file_service:
	pytest services/base/tests/ --cov=services/base/ --reruns 1 --reruns-delay 5
	pytest services/file_service/tests --cov=services/file_service/ --reruns 1 --reruns-delay 5

test_mobile_service:
	pytest services/base/tests/ --cov=services/base/ --reruns 1 --reruns-delay 5
	pytest services/mobile_service/tests --cov=services/mobile_service/ --reruns 1 --reruns-delay 5

test_user_service:
	pytest services/base/tests/ --cov=services/base/ --reruns 1 --reruns-delay 5
	pytest services/user_service/tests --cov=services/user_service/ --reruns 1 --reruns-delay 5

test_serverless:
	pytest services/serverless/apps/alexa_voice_log/tests/ --cov=services/serverless/apps/alexa_voice_log/ --reruns 1 --reruns-delay 5

	pytest services/serverless/apps/analytics_scheduler/tests/ --cov=services/serverless/apps/analytics_scheduler/ --reruns 1 --reruns-delay 5

	pytest services/serverless/apps/data_consistency_validator/tests/ --cov=services/serverless/apps/data_consistency_validator/ --reruns 1 --reruns-delay 5

	pytest services/serverless/apps/notify_handler/tests/ --cov=services/serverless/apps/notify_handler/ --reruns 1 --reruns-delay 5

	pytest services/serverless/apps/single_correlation_app/tests/ --cov=services/serverless/apps/single_correlation_app/ --reruns 1 --reruns-delay 5

	pytest services/serverless/apps/trend_insights/tests/ --cov=services/serverless/apps/trend_insights/ --reruns 1 --reruns-delay 5

	pytest services/serverless/apps/usage_statistics_generator/tests/ --cov=services/serverless/apps/usage_statistics_generator/ --reruns 1 --reruns-delay 5

test_serverless_integration: ## run integration specific tests
	pytest services/serverless/apps/usage_statistics_generator/tests/ --cov=services/serverless/apps/usage_statistics_generator/ -m integration --reruns 1 --reruns-delay 5

test_data_service_integration:
	pytest services/data_service --cov=services/data_service/ -m integration --reruns 1 --reruns-delay 5
