[{"logId": 28394477665, "dateOfSleep": "2020-08-10", "startTime": "2020-08-10T01:07:00.000", "endTime": "2020-08-10T06:43:30.000", "duration": 20160000, "minutesToFallAsleep": 0, "minutesAsleep": 301, "minutesAwake": 35, "minutesAfterWakeup": 0, "timeInBed": 336, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 45, "thirtyDayAvgMinutes": 40}, "wake": {"count": 16, "minutes": 35, "thirtyDayAvgMinutes": 51}, "light": {"count": 17, "minutes": 184, "thirtyDayAvgMinutes": 231}, "rem": {"count": 4, "minutes": 72, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-08-10T01:07:00.000", "level": "wake", "seconds": 720}, {"dateTime": "2020-08-10T01:19:00.000", "level": "light", "seconds": 1560}, {"dateTime": "2020-08-10T01:45:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-10T01:50:00.000", "level": "light", "seconds": 1710}, {"dateTime": "2020-08-10T02:18:30.000", "level": "rem", "seconds": 2280}, {"dateTime": "2020-08-10T02:56:30.000", "level": "light", "seconds": 4200}, {"dateTime": "2020-08-10T04:06:30.000", "level": "rem", "seconds": 1740}, {"dateTime": "2020-08-10T04:35:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-10T04:41:00.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-10T04:42:30.000", "level": "deep", "seconds": 2370}, {"dateTime": "2020-08-10T05:22:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-10T05:29:00.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-08-10T05:35:00.000", "level": "light", "seconds": 1950}, {"dateTime": "2020-08-10T06:07:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-10T06:13:30.000", "level": "light", "seconds": 1800}], "shortData": [{"dateTime": "2020-08-10T01:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:53:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:37:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T03:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T05:21:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:23:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:43:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T05:51:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T06:18:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:24:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:41:00.000", "level": "wake", "seconds": 150}]}, "mainSleep": true}, {"logId": 28382679124, "dateOfSleep": "2020-08-09", "startTime": "2020-08-09T01:56:00.000", "endTime": "2020-08-09T08:03:00.000", "duration": 22020000, "minutesToFallAsleep": 0, "minutesAsleep": 326, "minutesAwake": 41, "minutesAfterWakeup": 0, "timeInBed": 367, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 48, "thirtyDayAvgMinutes": 40}, "wake": {"count": 19, "minutes": 41, "thirtyDayAvgMinutes": 51}, "light": {"count": 20, "minutes": 226, "thirtyDayAvgMinutes": 231}, "rem": {"count": 3, "minutes": 52, "thirtyDayAvgMinutes": 61}}, "data": [{"dateTime": "2020-08-09T01:56:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T01:56:30.000", "level": "light", "seconds": 6300}, {"dateTime": "2020-08-09T03:41:30.000", "level": "deep", "seconds": 420}, {"dateTime": "2020-08-09T03:48:30.000", "level": "rem", "seconds": 1500}, {"dateTime": "2020-08-09T04:13:30.000", "level": "light", "seconds": 3210}, {"dateTime": "2020-08-09T05:07:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-09T05:11:30.000", "level": "light", "seconds": 240}, {"dateTime": "2020-08-09T05:15:30.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-09T05:22:30.000", "level": "light", "seconds": 2100}, {"dateTime": "2020-08-09T05:57:30.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-09T06:01:30.000", "level": "light", "seconds": 600}, {"dateTime": "2020-08-09T06:11:30.000", "level": "rem", "seconds": 1350}, {"dateTime": "2020-08-09T06:34:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-09T06:39:00.000", "level": "light", "seconds": 1500}, {"dateTime": "2020-08-09T07:04:00.000", "level": "deep", "seconds": 2520}, {"dateTime": "2020-08-09T07:46:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-08-09T07:56:30.000", "level": "wake", "seconds": 390}], "shortData": [{"dateTime": "2020-08-09T02:46:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T03:10:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T03:18:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-09T03:21:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-09T03:26:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T04:23:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T04:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T05:00:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-09T05:04:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T05:33:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T06:51:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T06:56:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-09T06:59:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T07:45:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28370288328, "dateOfSleep": "2020-08-08", "startTime": "2020-08-08T01:30:30.000", "endTime": "2020-08-08T07:19:30.000", "duration": 20940000, "minutesToFallAsleep": 0, "minutesAsleep": 313, "minutesAwake": 36, "minutesAfterWakeup": 0, "timeInBed": 349, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 46, "thirtyDayAvgMinutes": 40}, "wake": {"count": 22, "minutes": 36, "thirtyDayAvgMinutes": 51}, "light": {"count": 24, "minutes": 246, "thirtyDayAvgMinutes": 230}, "rem": {"count": 2, "minutes": 21, "thirtyDayAvgMinutes": 62}}, "data": [{"dateTime": "2020-08-08T01:30:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T01:31:00.000", "level": "light", "seconds": 240}, {"dateTime": "2020-08-08T01:35:00.000", "level": "deep", "seconds": 930}, {"dateTime": "2020-08-08T01:50:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-08T01:57:30.000", "level": "deep", "seconds": 1590}, {"dateTime": "2020-08-08T02:24:00.000", "level": "light", "seconds": 450}, {"dateTime": "2020-08-08T02:31:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-08T02:36:30.000", "level": "light", "seconds": 2820}, {"dateTime": "2020-08-08T03:23:30.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-08-08T03:32:30.000", "level": "light", "seconds": 8190}, {"dateTime": "2020-08-08T05:49:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-08T05:53:30.000", "level": "light", "seconds": 3210}, {"dateTime": "2020-08-08T06:47:00.000", "level": "rem", "seconds": 960}, {"dateTime": "2020-08-08T07:03:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-08T07:08:00.000", "level": "light", "seconds": 690}], "shortData": [{"dateTime": "2020-08-08T01:50:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T02:29:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T02:39:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T02:49:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T03:43:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-08T03:47:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-08T04:17:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T04:21:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T04:25:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-08T04:35:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T04:49:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T04:53:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T05:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T05:40:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T05:44:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T05:53:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-08T06:02:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T06:30:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T06:42:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28363184237, "dateOfSleep": "2020-08-07", "startTime": "2020-08-07T14:46:30.000", "endTime": "2020-08-07T15:46:30.000", "duration": 3600000, "minutesToFallAsleep": 0, "minutesAsleep": 52, "minutesAwake": 7, "minutesAfterWakeup": 1, "timeInBed": 60, "efficiency": 88, "type": "classic", "infoCode": 2, "levels": {"summary": {"restless": {"count": 4, "minutes": 7}, "awake": {"count": 1, "minutes": 1}, "asleep": {"count": 0, "minutes": 52}}, "data": [{"dateTime": "2020-08-07T14:46:30.000", "level": "asleep", "seconds": 180}, {"dateTime": "2020-08-07T14:49:30.000", "level": "restless", "seconds": 180}, {"dateTime": "2020-08-07T14:52:30.000", "level": "asleep", "seconds": 1680}, {"dateTime": "2020-08-07T15:20:30.000", "level": "restless", "seconds": 60}, {"dateTime": "2020-08-07T15:21:30.000", "level": "awake", "seconds": 60}, {"dateTime": "2020-08-07T15:22:30.000", "level": "asleep", "seconds": 1080}, {"dateTime": "2020-08-07T15:40:30.000", "level": "restless", "seconds": 120}, {"dateTime": "2020-08-07T15:42:30.000", "level": "asleep", "seconds": 180}, {"dateTime": "2020-08-07T15:45:30.000", "level": "restless", "seconds": 60}]}, "mainSleep": false}, {"logId": 28358361370, "dateOfSleep": "2020-08-07", "startTime": "2020-08-07T01:00:00.000", "endTime": "2020-08-07T07:25:30.000", "duration": 23100000, "minutesToFallAsleep": 0, "minutesAsleep": 340, "minutesAwake": 45, "minutesAfterWakeup": 7, "timeInBed": 385, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 24, "thirtyDayAvgMinutes": 40}, "wake": {"count": 17, "minutes": 45, "thirtyDayAvgMinutes": 52}, "light": {"count": 17, "minutes": 256, "thirtyDayAvgMinutes": 229}, "rem": {"count": 4, "minutes": 60, "thirtyDayAvgMinutes": 62}}, "data": [{"dateTime": "2020-08-07T01:00:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-07T01:05:00.000", "level": "light", "seconds": 2340}, {"dateTime": "2020-08-07T01:44:00.000", "level": "deep", "seconds": 390}, {"dateTime": "2020-08-07T01:50:30.000", "level": "light", "seconds": 1200}, {"dateTime": "2020-08-07T02:10:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-07T02:15:30.000", "level": "light", "seconds": 810}, {"dateTime": "2020-08-07T02:29:00.000", "level": "rem", "seconds": 900}, {"dateTime": "2020-08-07T02:44:00.000", "level": "light", "seconds": 450}, {"dateTime": "2020-08-07T02:51:30.000", "level": "deep", "seconds": 450}, {"dateTime": "2020-08-07T02:59:00.000", "level": "light", "seconds": 1680}, {"dateTime": "2020-08-07T03:27:00.000", "level": "rem", "seconds": 1740}, {"dateTime": "2020-08-07T03:56:00.000", "level": "light", "seconds": 2310}, {"dateTime": "2020-08-07T04:34:30.000", "level": "rem", "seconds": 1140}, {"dateTime": "2020-08-07T04:53:30.000", "level": "light", "seconds": 3420}, {"dateTime": "2020-08-07T05:50:30.000", "level": "wake", "seconds": 600}, {"dateTime": "2020-08-07T06:00:30.000", "level": "light", "seconds": 900}, {"dateTime": "2020-08-07T06:15:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-07T06:20:30.000", "level": "light", "seconds": 2370}, {"dateTime": "2020-08-07T07:00:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-08-07T07:06:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-08-07T07:18:00.000", "level": "wake", "seconds": 450}], "shortData": [{"dateTime": "2020-08-07T01:50:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-07T02:01:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-07T02:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T03:01:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T03:44:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T04:05:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T04:14:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T04:53:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T05:01:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-07T05:06:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T05:29:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-07T06:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T06:20:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28343598538, "dateOfSleep": "2020-08-06", "startTime": "2020-08-06T00:29:00.000", "endTime": "2020-08-06T07:29:30.000", "duration": 25200000, "minutesToFallAsleep": 0, "minutesAsleep": 368, "minutesAwake": 52, "minutesAfterWakeup": 0, "timeInBed": 420, "efficiency": 93, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 55, "thirtyDayAvgMinutes": 40}, "wake": {"count": 21, "minutes": 52, "thirtyDayAvgMinutes": 52}, "light": {"count": 21, "minutes": 208, "thirtyDayAvgMinutes": 230}, "rem": {"count": 6, "minutes": 105, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-08-06T00:29:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-06T00:34:00.000", "level": "light", "seconds": 30}, {"dateTime": "2020-08-06T00:34:30.000", "level": "deep", "seconds": 1800}, {"dateTime": "2020-08-06T01:04:30.000", "level": "light", "seconds": 1770}, {"dateTime": "2020-08-06T01:34:00.000", "level": "rem", "seconds": 1320}, {"dateTime": "2020-08-06T01:56:00.000", "level": "light", "seconds": 690}, {"dateTime": "2020-08-06T02:07:30.000", "level": "deep", "seconds": 270}, {"dateTime": "2020-08-06T02:12:00.000", "level": "light", "seconds": 1710}, {"dateTime": "2020-08-06T02:40:30.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-06T02:45:30.000", "level": "light", "seconds": 1020}, {"dateTime": "2020-08-06T03:02:30.000", "level": "rem", "seconds": 1320}, {"dateTime": "2020-08-06T03:24:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-08-06T03:31:00.000", "level": "light", "seconds": 1560}, {"dateTime": "2020-08-06T03:57:00.000", "level": "deep", "seconds": 1380}, {"dateTime": "2020-08-06T04:20:00.000", "level": "light", "seconds": 1410}, {"dateTime": "2020-08-06T04:43:30.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-08-06T04:48:00.000", "level": "light", "seconds": 1860}, {"dateTime": "2020-08-06T05:19:00.000", "level": "rem", "seconds": 2280}, {"dateTime": "2020-08-06T05:57:00.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-06T05:58:30.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-06T06:03:30.000", "level": "rem", "seconds": 300}, {"dateTime": "2020-08-06T06:08:30.000", "level": "light", "seconds": 2490}, {"dateTime": "2020-08-06T06:50:00.000", "level": "rem", "seconds": 1140}, {"dateTime": "2020-08-06T07:09:00.000", "level": "light", "seconds": 360}, {"dateTime": "2020-08-06T07:15:00.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-08-06T07:24:00.000", "level": "light", "seconds": 120}, {"dateTime": "2020-08-06T07:26:00.000", "level": "wake", "seconds": 210}], "shortData": [{"dateTime": "2020-08-06T01:04:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-06T01:08:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-06T01:55:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-06T02:12:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-06T04:18:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-06T04:23:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-06T04:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-06T04:41:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-06T05:00:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-06T05:05:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-06T05:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-06T06:11:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-06T06:14:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-06T06:40:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28327639714, "dateOfSleep": "2020-08-05", "startTime": "2020-08-05T01:18:30.000", "endTime": "2020-08-05T07:16:30.000", "duration": 21480000, "minutesToFallAsleep": 0, "minutesAsleep": 311, "minutesAwake": 47, "minutesAfterWakeup": 0, "timeInBed": 358, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 27, "thirtyDayAvgMinutes": 40}, "wake": {"count": 21, "minutes": 47, "thirtyDayAvgMinutes": 52}, "light": {"count": 23, "minutes": 244, "thirtyDayAvgMinutes": 230}, "rem": {"count": 4, "minutes": 40, "thirtyDayAvgMinutes": 61}}, "data": [{"dateTime": "2020-08-05T01:18:30.000", "level": "light", "seconds": 2040}, {"dateTime": "2020-08-05T01:52:30.000", "level": "deep", "seconds": 1320}, {"dateTime": "2020-08-05T02:14:30.000", "level": "light", "seconds": 1770}, {"dateTime": "2020-08-05T02:44:00.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-08-05T02:50:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-05T02:56:00.000", "level": "light", "seconds": 3180}, {"dateTime": "2020-08-05T03:49:00.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-08-05T03:58:30.000", "level": "light", "seconds": 810}, {"dateTime": "2020-08-05T04:12:00.000", "level": "rem", "seconds": 660}, {"dateTime": "2020-08-05T04:23:00.000", "level": "light", "seconds": 540}, {"dateTime": "2020-08-05T04:32:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-08-05T04:40:00.000", "level": "light", "seconds": 3480}, {"dateTime": "2020-08-05T05:38:00.000", "level": "rem", "seconds": 1110}, {"dateTime": "2020-08-05T05:56:30.000", "level": "light", "seconds": 2970}, {"dateTime": "2020-08-05T06:46:00.000", "level": "rem", "seconds": 390}, {"dateTime": "2020-08-05T06:52:30.000", "level": "light", "seconds": 810}, {"dateTime": "2020-08-05T07:06:00.000", "level": "wake", "seconds": 630}], "shortData": [{"dateTime": "2020-08-05T02:13:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-05T02:20:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-05T03:04:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-05T03:08:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-05T04:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-05T04:50:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-05T05:00:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-05T05:18:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-05T05:23:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-05T05:30:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-05T05:55:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-05T05:58:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-05T06:10:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-05T06:14:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-05T06:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-05T06:51:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-05T06:55:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-05T06:59:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28311695139, "dateOfSleep": "2020-08-04", "startTime": "2020-08-03T23:56:00.000", "endTime": "2020-08-04T06:38:30.000", "duration": 24120000, "minutesToFallAsleep": 0, "minutesAsleep": 354, "minutesAwake": 48, "minutesAfterWakeup": 0, "timeInBed": 402, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 38, "thirtyDayAvgMinutes": 41}, "wake": {"count": 23, "minutes": 48, "thirtyDayAvgMinutes": 52}, "light": {"count": 26, "minutes": 258, "thirtyDayAvgMinutes": 228}, "rem": {"count": 5, "minutes": 58, "thirtyDayAvgMinutes": 61}}, "data": [{"dateTime": "2020-08-03T23:56:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-04T00:01:00.000", "level": "light", "seconds": 930}, {"dateTime": "2020-08-04T00:16:30.000", "level": "deep", "seconds": 330}, {"dateTime": "2020-08-04T00:22:00.000", "level": "light", "seconds": 1110}, {"dateTime": "2020-08-04T00:40:30.000", "level": "deep", "seconds": 330}, {"dateTime": "2020-08-04T00:46:00.000", "level": "light", "seconds": 60}, {"dateTime": "2020-08-04T00:47:00.000", "level": "deep", "seconds": 270}, {"dateTime": "2020-08-04T00:51:30.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-08-04T00:56:00.000", "level": "light", "seconds": 2940}, {"dateTime": "2020-08-04T01:45:00.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-08-04T01:54:30.000", "level": "light", "seconds": 1740}, {"dateTime": "2020-08-04T02:23:30.000", "level": "rem", "seconds": 1440}, {"dateTime": "2020-08-04T02:47:30.000", "level": "light", "seconds": 1380}, {"dateTime": "2020-08-04T03:10:30.000", "level": "deep", "seconds": 1470}, {"dateTime": "2020-08-04T03:35:00.000", "level": "light", "seconds": 3840}, {"dateTime": "2020-08-04T04:39:00.000", "level": "rem", "seconds": 810}, {"dateTime": "2020-08-04T04:52:30.000", "level": "light", "seconds": 3270}, {"dateTime": "2020-08-04T05:47:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-04T05:51:30.000", "level": "light", "seconds": 510}, {"dateTime": "2020-08-04T06:00:00.000", "level": "rem", "seconds": 1110}, {"dateTime": "2020-08-04T06:18:30.000", "level": "light", "seconds": 660}, {"dateTime": "2020-08-04T06:29:30.000", "level": "wake", "seconds": 540}], "shortData": [{"dateTime": "2020-08-04T01:17:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-04T01:29:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-04T01:33:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-04T01:57:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-04T02:46:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-04T02:50:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-04T02:52:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-04T03:32:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-04T03:38:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-04T03:42:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-04T03:56:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-04T04:11:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-04T04:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-04T04:24:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-04T04:50:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-04T04:56:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-04T05:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-04T05:41:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-04T05:55:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28294999488, "dateOfSleep": "2020-08-03", "startTime": "2020-08-03T00:30:30.000", "endTime": "2020-08-03T07:40:30.000", "duration": 25800000, "minutesToFallAsleep": 0, "minutesAsleep": 355, "minutesAwake": 75, "minutesAfterWakeup": 0, "timeInBed": 430, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 36, "thirtyDayAvgMinutes": 41}, "wake": {"count": 19, "minutes": 75, "thirtyDayAvgMinutes": 51}, "light": {"count": 18, "minutes": 280, "thirtyDayAvgMinutes": 226}, "rem": {"count": 2, "minutes": 39, "thirtyDayAvgMinutes": 62}}, "data": [{"dateTime": "2020-08-03T00:30:30.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-08-03T00:39:30.000", "level": "light", "seconds": 1860}, {"dateTime": "2020-08-03T01:10:30.000", "level": "deep", "seconds": 1890}, {"dateTime": "2020-08-03T01:42:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-08-03T01:50:00.000", "level": "light", "seconds": 1530}, {"dateTime": "2020-08-03T02:15:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-03T02:21:00.000", "level": "light", "seconds": 3330}, {"dateTime": "2020-08-03T03:16:30.000", "level": "rem", "seconds": 2490}, {"dateTime": "2020-08-03T03:58:00.000", "level": "light", "seconds": 1350}, {"dateTime": "2020-08-03T04:20:30.000", "level": "wake", "seconds": 660}, {"dateTime": "2020-08-03T04:31:30.000", "level": "light", "seconds": 2070}, {"dateTime": "2020-08-03T05:06:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-08-03T05:12:30.000", "level": "light", "seconds": 1470}, {"dateTime": "2020-08-03T05:37:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-03T05:41:00.000", "level": "light", "seconds": 1590}, {"dateTime": "2020-08-03T06:07:30.000", "level": "deep", "seconds": 270}, {"dateTime": "2020-08-03T06:12:00.000", "level": "light", "seconds": 1260}, {"dateTime": "2020-08-03T06:33:00.000", "level": "wake", "seconds": 690}, {"dateTime": "2020-08-03T06:44:30.000", "level": "light", "seconds": 300}, {"dateTime": "2020-08-03T06:49:30.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-03T06:53:30.000", "level": "light", "seconds": 2460}, {"dateTime": "2020-08-03T07:34:30.000", "level": "wake", "seconds": 360}], "shortData": [{"dateTime": "2020-08-03T02:48:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-03T03:52:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-03T03:56:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-03T05:18:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-03T06:17:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-03T06:24:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-03T07:05:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-03T07:21:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-03T07:24:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-03T07:28:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28282386636, "dateOfSleep": "2020-08-02", "startTime": "2020-08-02T01:50:00.000", "endTime": "2020-08-02T08:16:30.000", "duration": 23160000, "minutesToFallAsleep": 0, "minutesAsleep": 336, "minutesAwake": 50, "minutesAfterWakeup": 0, "timeInBed": 386, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 55, "thirtyDayAvgMinutes": 40}, "wake": {"count": 17, "minutes": 50, "thirtyDayAvgMinutes": 51}, "light": {"count": 17, "minutes": 218, "thirtyDayAvgMinutes": 226}, "rem": {"count": 5, "minutes": 63, "thirtyDayAvgMinutes": 62}}, "data": [{"dateTime": "2020-08-02T01:50:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-02T01:55:30.000", "level": "light", "seconds": 2490}, {"dateTime": "2020-08-02T02:37:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-02T02:43:00.000", "level": "light", "seconds": 1320}, {"dateTime": "2020-08-02T03:05:00.000", "level": "rem", "seconds": 780}, {"dateTime": "2020-08-02T03:18:00.000", "level": "light", "seconds": 2880}, {"dateTime": "2020-08-02T04:06:00.000", "level": "rem", "seconds": 1860}, {"dateTime": "2020-08-02T04:37:00.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-08-02T04:46:00.000", "level": "light", "seconds": 1080}, {"dateTime": "2020-08-02T05:04:00.000", "level": "deep", "seconds": 1320}, {"dateTime": "2020-08-02T05:26:00.000", "level": "light", "seconds": 480}, {"dateTime": "2020-08-02T05:34:00.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-02T05:39:30.000", "level": "light", "seconds": 660}, {"dateTime": "2020-08-02T05:50:30.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-02T05:58:00.000", "level": "light", "seconds": 1020}, {"dateTime": "2020-08-02T06:15:00.000", "level": "deep", "seconds": 1740}, {"dateTime": "2020-08-02T06:44:00.000", "level": "rem", "seconds": 930}, {"dateTime": "2020-08-02T06:59:30.000", "level": "light", "seconds": 2190}, {"dateTime": "2020-08-02T07:36:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-02T07:40:00.000", "level": "light", "seconds": 1620}, {"dateTime": "2020-08-02T08:07:00.000", "level": "wake", "seconds": 570}], "shortData": [{"dateTime": "2020-08-02T02:18:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-02T02:56:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-02T03:26:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-02T03:34:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-02T04:34:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-02T05:23:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-02T05:39:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-02T06:59:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-02T07:04:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-02T07:10:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-02T07:32:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-02T07:43:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28270854367, "dateOfSleep": "2020-08-01", "startTime": "2020-08-01T01:35:00.000", "endTime": "2020-08-01T07:53:00.000", "duration": 22680000, "minutesToFallAsleep": 0, "minutesAsleep": 313, "minutesAwake": 65, "minutesAfterWakeup": 0, "timeInBed": 378, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 18, "thirtyDayAvgMinutes": 41}, "wake": {"count": 13, "minutes": 65, "thirtyDayAvgMinutes": 50}, "light": {"count": 14, "minutes": 203, "thirtyDayAvgMinutes": 228}, "rem": {"count": 4, "minutes": 92, "thirtyDayAvgMinutes": 61}}, "data": [{"dateTime": "2020-08-01T01:35:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-01T01:35:30.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-08-01T01:55:00.000", "level": "rem", "seconds": 540}, {"dateTime": "2020-08-01T02:04:00.000", "level": "light", "seconds": 780}, {"dateTime": "2020-08-01T02:17:00.000", "level": "deep", "seconds": 780}, {"dateTime": "2020-08-01T02:30:00.000", "level": "light", "seconds": 1890}, {"dateTime": "2020-08-01T03:01:30.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-08-01T03:11:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-08-01T03:23:00.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-08-01T03:30:30.000", "level": "light", "seconds": 510}, {"dateTime": "2020-08-01T03:39:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-08-01T03:47:00.000", "level": "light", "seconds": 990}, {"dateTime": "2020-08-01T04:03:30.000", "level": "rem", "seconds": 2520}, {"dateTime": "2020-08-01T04:45:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-01T04:52:30.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-08-01T04:57:00.000", "level": "light", "seconds": 1050}, {"dateTime": "2020-08-01T05:14:30.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-08-01T05:25:00.000", "level": "light", "seconds": 1980}, {"dateTime": "2020-08-01T05:58:00.000", "level": "rem", "seconds": 1440}, {"dateTime": "2020-08-01T06:22:00.000", "level": "light", "seconds": 1980}, {"dateTime": "2020-08-01T06:55:00.000", "level": "deep", "seconds": 330}, {"dateTime": "2020-08-01T07:00:30.000", "level": "light", "seconds": 990}, {"dateTime": "2020-08-01T07:17:00.000", "level": "rem", "seconds": 1020}, {"dateTime": "2020-08-01T07:34:00.000", "level": "wake", "seconds": 810}, {"dateTime": "2020-08-01T07:47:30.000", "level": "light", "seconds": 120}, {"dateTime": "2020-08-01T07:49:30.000", "level": "wake", "seconds": 210}], "shortData": [{"dateTime": "2020-08-01T02:29:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-01T02:40:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-01T03:13:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-01T06:22:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-01T07:00:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28257995755, "dateOfSleep": "2020-07-31", "startTime": "2020-07-31T00:19:00.000", "endTime": "2020-07-31T06:43:30.000", "duration": 23040000, "minutesToFallAsleep": 0, "minutesAsleep": 336, "minutesAwake": 48, "minutesAfterWakeup": 0, "timeInBed": 384, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 13, "thirtyDayAvgMinutes": 43}, "wake": {"count": 20, "minutes": 48, "thirtyDayAvgMinutes": 50}, "light": {"count": 20, "minutes": 261, "thirtyDayAvgMinutes": 226}, "rem": {"count": 4, "minutes": 62, "thirtyDayAvgMinutes": 61}}, "data": [{"dateTime": "2020-07-31T00:19:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-31T00:25:30.000", "level": "light", "seconds": 2640}, {"dateTime": "2020-07-31T01:09:30.000", "level": "deep", "seconds": 750}, {"dateTime": "2020-07-31T01:22:00.000", "level": "light", "seconds": 5910}, {"dateTime": "2020-07-31T03:00:30.000", "level": "rem", "seconds": 2100}, {"dateTime": "2020-07-31T03:35:30.000", "level": "light", "seconds": 3000}, {"dateTime": "2020-07-31T04:25:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-31T04:31:00.000", "level": "light", "seconds": 1950}, {"dateTime": "2020-07-31T05:03:30.000", "level": "wake", "seconds": 840}, {"dateTime": "2020-07-31T05:17:30.000", "level": "light", "seconds": 2760}, {"dateTime": "2020-07-31T06:03:30.000", "level": "rem", "seconds": 1410}, {"dateTime": "2020-07-31T06:27:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-07-31T06:34:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-07-31T06:38:30.000", "level": "light", "seconds": 30}, {"dateTime": "2020-07-31T06:39:00.000", "level": "wake", "seconds": 270}], "shortData": [{"dateTime": "2020-07-31T00:38:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-31T00:46:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-31T00:54:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-31T01:22:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-31T01:57:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-31T02:01:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-31T02:06:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-31T02:20:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-31T02:24:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-31T02:33:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-31T03:19:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-31T03:35:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-31T04:22:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-31T04:48:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-31T05:20:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-31T05:35:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28242229863, "dateOfSleep": "2020-07-30", "startTime": "2020-07-29T23:49:30.000", "endTime": "2020-07-30T06:24:30.000", "duration": 23700000, "minutesToFallAsleep": 0, "minutesAsleep": 334, "minutesAwake": 61, "minutesAfterWakeup": 0, "timeInBed": 395, "efficiency": 91, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 60, "thirtyDayAvgMinutes": 42}, "wake": {"count": 20, "minutes": 61, "thirtyDayAvgMinutes": 50}, "light": {"count": 22, "minutes": 196, "thirtyDayAvgMinutes": 227}, "rem": {"count": 5, "minutes": 78, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-07-29T23:49:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-29T23:52:00.000", "level": "light", "seconds": 30}, {"dateTime": "2020-07-29T23:52:30.000", "level": "deep", "seconds": 2580}, {"dateTime": "2020-07-30T00:35:30.000", "level": "light", "seconds": 450}, {"dateTime": "2020-07-30T00:43:00.000", "level": "deep", "seconds": 1230}, {"dateTime": "2020-07-30T01:03:30.000", "level": "light", "seconds": 30}, {"dateTime": "2020-07-30T01:04:00.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-07-30T01:09:30.000", "level": "light", "seconds": 540}, {"dateTime": "2020-07-30T01:18:30.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-07-30T01:28:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-07-30T01:40:00.000", "level": "wake", "seconds": 660}, {"dateTime": "2020-07-30T01:51:00.000", "level": "light", "seconds": 1650}, {"dateTime": "2020-07-30T02:18:30.000", "level": "rem", "seconds": 2820}, {"dateTime": "2020-07-30T03:05:30.000", "level": "light", "seconds": 4500}, {"dateTime": "2020-07-30T04:20:30.000", "level": "rem", "seconds": 1260}, {"dateTime": "2020-07-30T04:41:30.000", "level": "light", "seconds": 870}, {"dateTime": "2020-07-30T04:56:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-30T05:01:00.000", "level": "light", "seconds": 2010}, {"dateTime": "2020-07-30T05:34:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-07-30T05:40:00.000", "level": "light", "seconds": 60}, {"dateTime": "2020-07-30T05:41:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-07-30T05:44:30.000", "level": "light", "seconds": 1500}, {"dateTime": "2020-07-30T06:09:30.000", "level": "wake", "seconds": 900}], "shortData": [{"dateTime": "2020-07-29T23:49:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-30T01:00:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-30T01:11:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-30T03:04:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-30T03:16:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T03:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T03:49:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-30T03:56:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T04:29:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T04:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T04:48:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T05:09:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T05:12:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-30T05:55:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-30T05:59:30.000", "level": "wake", "seconds": 90}]}, "mainSleep": true}, {"logId": 28230772358, "dateOfSleep": "2020-07-29", "startTime": "2020-07-29T00:47:00.000", "endTime": "2020-07-29T06:54:00.000", "duration": 22020000, "minutesToFallAsleep": 0, "minutesAsleep": 298, "minutesAwake": 69, "minutesAfterWakeup": 1, "timeInBed": 367, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 59, "thirtyDayAvgMinutes": 41}, "wake": {"count": 15, "minutes": 69, "thirtyDayAvgMinutes": 49}, "light": {"count": 17, "minutes": 191, "thirtyDayAvgMinutes": 230}, "rem": {"count": 4, "minutes": 48, "thirtyDayAvgMinutes": 61}}, "data": [{"dateTime": "2020-07-29T00:47:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-29T00:53:30.000", "level": "light", "seconds": 270}, {"dateTime": "2020-07-29T00:58:00.000", "level": "deep", "seconds": 2280}, {"dateTime": "2020-07-29T01:36:00.000", "level": "light", "seconds": 570}, {"dateTime": "2020-07-29T01:45:30.000", "level": "rem", "seconds": 930}, {"dateTime": "2020-07-29T02:01:00.000", "level": "light", "seconds": 930}, {"dateTime": "2020-07-29T02:16:30.000", "level": "deep", "seconds": 1320}, {"dateTime": "2020-07-29T02:38:30.000", "level": "light", "seconds": 1350}, {"dateTime": "2020-07-29T03:01:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-29T03:06:30.000", "level": "light", "seconds": 2130}, {"dateTime": "2020-07-29T03:42:00.000", "level": "rem", "seconds": 510}, {"dateTime": "2020-07-29T03:50:30.000", "level": "light", "seconds": 150}, {"dateTime": "2020-07-29T03:53:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-07-29T04:01:00.000", "level": "light", "seconds": 510}, {"dateTime": "2020-07-29T04:09:30.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-29T04:15:30.000", "level": "light", "seconds": 150}, {"dateTime": "2020-07-29T04:18:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-29T04:24:30.000", "level": "light", "seconds": 1710}, {"dateTime": "2020-07-29T04:53:00.000", "level": "rem", "seconds": 1470}, {"dateTime": "2020-07-29T05:17:30.000", "level": "light", "seconds": 3180}, {"dateTime": "2020-07-29T06:10:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-29T06:17:00.000", "level": "light", "seconds": 150}, {"dateTime": "2020-07-29T06:19:30.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-07-29T06:28:30.000", "level": "light", "seconds": 750}, {"dateTime": "2020-07-29T06:41:00.000", "level": "wake", "seconds": 780}], "shortData": [{"dateTime": "2020-07-29T01:35:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-29T03:14:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-29T03:25:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-29T03:31:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-29T05:08:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-29T05:21:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-29T06:08:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28218308671, "dateOfSleep": "2020-07-28", "startTime": "2020-07-27T22:58:00.000", "endTime": "2020-07-28T07:14:30.000", "duration": 29760000, "minutesToFallAsleep": 0, "minutesAsleep": 420, "minutesAwake": 76, "minutesAfterWakeup": 9, "timeInBed": 496, "efficiency": 93, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 68, "thirtyDayAvgMinutes": 39}, "wake": {"count": 25, "minutes": 76, "thirtyDayAvgMinutes": 47}, "light": {"count": 29, "minutes": 244, "thirtyDayAvgMinutes": 229}, "rem": {"count": 5, "minutes": 108, "thirtyDayAvgMinutes": 58}}, "data": [{"dateTime": "2020-07-27T22:58:00.000", "level": "light", "seconds": 330}, {"dateTime": "2020-07-27T23:03:30.000", "level": "deep", "seconds": 1170}, {"dateTime": "2020-07-27T23:23:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-27T23:28:00.000", "level": "light", "seconds": 1740}, {"dateTime": "2020-07-27T23:57:00.000", "level": "rem", "seconds": 1410}, {"dateTime": "2020-07-28T00:20:30.000", "level": "light", "seconds": 90}, {"dateTime": "2020-07-28T00:22:00.000", "level": "wake", "seconds": 1080}, {"dateTime": "2020-07-28T00:40:00.000", "level": "light", "seconds": 1230}, {"dateTime": "2020-07-28T01:00:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-28T01:06:00.000", "level": "light", "seconds": 2160}, {"dateTime": "2020-07-28T01:42:00.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-07-28T01:49:00.000", "level": "light", "seconds": 1020}, {"dateTime": "2020-07-28T02:06:00.000", "level": "wake", "seconds": 600}, {"dateTime": "2020-07-28T02:16:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-07-28T02:28:00.000", "level": "rem", "seconds": 2340}, {"dateTime": "2020-07-28T03:07:00.000", "level": "light", "seconds": 870}, {"dateTime": "2020-07-28T03:21:30.000", "level": "deep", "seconds": 1560}, {"dateTime": "2020-07-28T03:47:30.000", "level": "light", "seconds": 3570}, {"dateTime": "2020-07-28T04:47:00.000", "level": "rem", "seconds": 1140}, {"dateTime": "2020-07-28T05:06:00.000", "level": "light", "seconds": 2340}, {"dateTime": "2020-07-28T05:45:00.000", "level": "deep", "seconds": 1380}, {"dateTime": "2020-07-28T06:08:00.000", "level": "light", "seconds": 120}, {"dateTime": "2020-07-28T06:10:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-07-28T06:18:00.000", "level": "light", "seconds": 450}, {"dateTime": "2020-07-28T06:25:30.000", "level": "rem", "seconds": 1650}, {"dateTime": "2020-07-28T06:53:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-07-28T07:05:00.000", "level": "wake", "seconds": 570}], "shortData": [{"dateTime": "2020-07-27T23:47:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-28T00:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T01:18:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T01:52:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-28T01:55:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T01:57:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T02:19:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-28T02:21:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-28T03:09:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T03:59:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T04:06:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T04:20:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-28T04:56:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T05:15:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-28T05:24:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T06:07:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-28T06:57:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-28T07:03:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28202963076, "dateOfSleep": "2020-07-27", "startTime": "2020-07-27T01:42:30.000", "endTime": "2020-07-27T07:10:30.000", "duration": 19680000, "minutesToFallAsleep": 0, "minutesAsleep": 278, "minutesAwake": 50, "minutesAfterWakeup": 0, "timeInBed": 328, "efficiency": 90, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 28, "thirtyDayAvgMinutes": 40}, "wake": {"count": 16, "minutes": 50, "thirtyDayAvgMinutes": 47}, "light": {"count": 16, "minutes": 197, "thirtyDayAvgMinutes": 231}, "rem": {"count": 3, "minutes": 53, "thirtyDayAvgMinutes": 58}}, "data": [{"dateTime": "2020-07-27T01:42:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-27T01:43:00.000", "level": "light", "seconds": 2670}, {"dateTime": "2020-07-27T02:27:30.000", "level": "rem", "seconds": 720}, {"dateTime": "2020-07-27T02:39:30.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-07-27T02:49:00.000", "level": "light", "seconds": 1800}, {"dateTime": "2020-07-27T03:19:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-07-27T03:25:00.000", "level": "light", "seconds": 180}, {"dateTime": "2020-07-27T03:28:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-27T03:33:30.000", "level": "light", "seconds": 840}, {"dateTime": "2020-07-27T03:47:30.000", "level": "rem", "seconds": 2460}, {"dateTime": "2020-07-27T04:28:30.000", "level": "light", "seconds": 3660}, {"dateTime": "2020-07-27T05:29:30.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-07-27T05:38:30.000", "level": "light", "seconds": 240}, {"dateTime": "2020-07-27T05:42:30.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-27T05:47:30.000", "level": "light", "seconds": 2820}, {"dateTime": "2020-07-27T06:34:30.000", "level": "deep", "seconds": 690}, {"dateTime": "2020-07-27T06:46:00.000", "level": "light", "seconds": 330}, {"dateTime": "2020-07-27T06:51:30.000", "level": "deep", "seconds": 690}, {"dateTime": "2020-07-27T07:03:00.000", "level": "wake", "seconds": 450}], "shortData": [{"dateTime": "2020-07-27T01:42:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-27T02:01:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-27T02:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-27T03:24:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-27T04:26:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-27T04:29:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-27T05:02:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-27T05:21:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-27T05:54:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-27T06:01:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-27T06:09:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28189914679, "dateOfSleep": "2020-07-26", "startTime": "2020-07-26T02:22:30.000", "endTime": "2020-07-26T08:06:30.000", "duration": 20640000, "minutesToFallAsleep": 0, "minutesAsleep": 294, "minutesAwake": 50, "minutesAfterWakeup": 0, "timeInBed": 344, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 20, "thirtyDayAvgMinutes": 41}, "wake": {"count": 17, "minutes": 50, "thirtyDayAvgMinutes": 47}, "light": {"count": 17, "minutes": 240, "thirtyDayAvgMinutes": 230}, "rem": {"count": 3, "minutes": 34, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-07-26T02:22:30.000", "level": "light", "seconds": 30}, {"dateTime": "2020-07-26T02:23:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-07-26T02:31:00.000", "level": "light", "seconds": 2100}, {"dateTime": "2020-07-26T03:06:00.000", "level": "deep", "seconds": 810}, {"dateTime": "2020-07-26T03:19:30.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-07-26T03:29:00.000", "level": "light", "seconds": 2040}, {"dateTime": "2020-07-26T04:03:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-07-26T04:09:00.000", "level": "rem", "seconds": 1800}, {"dateTime": "2020-07-26T04:39:00.000", "level": "light", "seconds": 3630}, {"dateTime": "2020-07-26T05:39:30.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-26T05:45:30.000", "level": "light", "seconds": 600}, {"dateTime": "2020-07-26T05:55:30.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-07-26T06:00:00.000", "level": "light", "seconds": 6840}, {"dateTime": "2020-07-26T07:54:00.000", "level": "wake", "seconds": 750}], "shortData": [{"dateTime": "2020-07-26T03:39:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-26T04:28:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-26T04:43:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-26T05:14:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-26T06:00:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-26T06:11:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-26T06:50:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-26T07:11:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-26T07:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-26T07:27:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-26T07:29:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-26T07:34:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-26T07:40:00.000", "level": "wake", "seconds": 90}]}, "mainSleep": true}, {"logId": 28182766212, "dateOfSleep": "2020-07-25", "startTime": "2020-07-25T16:43:00.000", "endTime": "2020-07-25T18:14:00.000", "duration": 5460000, "minutesToFallAsleep": 0, "minutesAsleep": 84, "minutesAwake": 7, "minutesAfterWakeup": 0, "timeInBed": 91, "efficiency": 92, "type": "classic", "infoCode": 2, "levels": {"summary": {"restless": {"count": 2, "minutes": 3}, "awake": {"count": 1, "minutes": 4}, "asleep": {"count": 0, "minutes": 84}}, "data": [{"dateTime": "2020-07-25T16:43:00.000", "level": "awake", "seconds": 240}, {"dateTime": "2020-07-25T16:47:00.000", "level": "restless", "seconds": 120}, {"dateTime": "2020-07-25T16:49:00.000", "level": "asleep", "seconds": 4380}, {"dateTime": "2020-07-25T18:02:00.000", "level": "restless", "seconds": 60}, {"dateTime": "2020-07-25T18:03:00.000", "level": "asleep", "seconds": 660}]}, "mainSleep": false}, {"logId": 28181787556, "dateOfSleep": "2020-07-25", "startTime": "2020-07-25T12:37:00.000", "endTime": "2020-07-25T13:37:30.000", "duration": 3600000, "minutesToFallAsleep": 0, "minutesAsleep": 55, "minutesAwake": 5, "minutesAfterWakeup": 0, "timeInBed": 60, "efficiency": 92, "type": "classic", "infoCode": 2, "levels": {"summary": {"restless": {"count": 2, "minutes": 5}, "awake": {"count": 0, "minutes": 0}, "asleep": {"count": 0, "minutes": 55}}, "data": [{"dateTime": "2020-07-25T12:37:00.000", "level": "restless", "seconds": 180}, {"dateTime": "2020-07-25T12:40:00.000", "level": "asleep", "seconds": 3240}, {"dateTime": "2020-07-25T13:34:00.000", "level": "restless", "seconds": 120}, {"dateTime": "2020-07-25T13:36:00.000", "level": "asleep", "seconds": 60}]}, "mainSleep": false}, {"logId": 28176871569, "dateOfSleep": "2020-07-25", "startTime": "2020-07-25T01:15:00.000", "endTime": "2020-07-25T07:01:30.000", "duration": 20760000, "minutesToFallAsleep": 0, "minutesAsleep": 294, "minutesAwake": 52, "minutesAfterWakeup": 0, "timeInBed": 346, "efficiency": 97, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 44, "thirtyDayAvgMinutes": 41}, "wake": {"count": 20, "minutes": 52, "thirtyDayAvgMinutes": 46}, "light": {"count": 23, "minutes": 197, "thirtyDayAvgMinutes": 233}, "rem": {"count": 3, "minutes": 53, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-07-25T01:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-25T01:15:30.000", "level": "light", "seconds": 1410}, {"dateTime": "2020-07-25T01:39:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-07-25T01:45:00.000", "level": "light", "seconds": 1770}, {"dateTime": "2020-07-25T02:14:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-07-25T02:20:30.000", "level": "light", "seconds": 840}, {"dateTime": "2020-07-25T02:34:30.000", "level": "rem", "seconds": 810}, {"dateTime": "2020-07-25T02:48:00.000", "level": "light", "seconds": 3540}, {"dateTime": "2020-07-25T03:47:00.000", "level": "rem", "seconds": 2130}, {"dateTime": "2020-07-25T04:22:30.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-25T04:28:30.000", "level": "light", "seconds": 210}, {"dateTime": "2020-07-25T04:32:00.000", "level": "deep", "seconds": 1890}, {"dateTime": "2020-07-25T05:03:30.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-07-25T05:14:00.000", "level": "light", "seconds": 1920}, {"dateTime": "2020-07-25T05:46:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-07-25T05:50:30.000", "level": "light", "seconds": 1860}, {"dateTime": "2020-07-25T06:21:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-07-25T06:25:00.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-07-25T06:44:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-07-25T06:48:00.000", "level": "light", "seconds": 180}, {"dateTime": "2020-07-25T06:51:00.000", "level": "wake", "seconds": 630}], "shortData": [{"dateTime": "2020-07-25T01:15:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-25T01:22:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-25T01:51:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-25T01:54:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-25T02:01:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-25T02:49:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-25T03:04:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-25T03:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-25T05:24:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-25T05:39:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-25T05:53:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-25T05:57:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-25T06:02:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-25T06:05:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-25T06:32:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28165545345, "dateOfSleep": "2020-07-24", "startTime": "2020-07-24T00:19:00.000", "endTime": "2020-07-24T06:35:00.000", "duration": 22560000, "minutesToFallAsleep": 0, "minutesAsleep": 321, "minutesAwake": 55, "minutesAfterWakeup": 0, "timeInBed": 376, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 49, "thirtyDayAvgMinutes": 40}, "wake": {"count": 23, "minutes": 55, "thirtyDayAvgMinutes": 46}, "light": {"count": 22, "minutes": 206, "thirtyDayAvgMinutes": 235}, "rem": {"count": 6, "minutes": 66, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-07-24T00:19:00.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-07-24T00:29:30.000", "level": "deep", "seconds": 2070}, {"dateTime": "2020-07-24T01:04:00.000", "level": "light", "seconds": 600}, {"dateTime": "2020-07-24T01:14:00.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-07-24T01:22:00.000", "level": "light", "seconds": 150}, {"dateTime": "2020-07-24T01:24:30.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-07-24T01:33:30.000", "level": "light", "seconds": 3330}, {"dateTime": "2020-07-24T02:29:00.000", "level": "rem", "seconds": 1350}, {"dateTime": "2020-07-24T02:51:30.000", "level": "light", "seconds": 1380}, {"dateTime": "2020-07-24T03:14:30.000", "level": "rem", "seconds": 1350}, {"dateTime": "2020-07-24T03:37:00.000", "level": "light", "seconds": 2400}, {"dateTime": "2020-07-24T04:17:00.000", "level": "rem", "seconds": 930}, {"dateTime": "2020-07-24T04:32:30.000", "level": "light", "seconds": 1710}, {"dateTime": "2020-07-24T05:01:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-24T05:07:30.000", "level": "light", "seconds": 2730}, {"dateTime": "2020-07-24T05:53:00.000", "level": "deep", "seconds": 900}, {"dateTime": "2020-07-24T06:08:00.000", "level": "rem", "seconds": 390}, {"dateTime": "2020-07-24T06:14:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-07-24T06:21:30.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-07-24T06:32:00.000", "level": "light", "seconds": 180}], "shortData": [{"dateTime": "2020-07-24T01:03:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T01:10:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T01:40:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T01:55:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T02:12:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-24T02:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T02:52:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T03:09:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-24T03:28:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T03:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T04:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T05:10:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-24T05:31:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T05:36:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T05:44:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T06:07:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T06:19:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-24T06:34:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28150940566, "dateOfSleep": "2020-07-23", "startTime": "2020-07-23T00:32:00.000", "endTime": "2020-07-23T07:45:30.000", "duration": 25980000, "minutesToFallAsleep": 0, "minutesAsleep": 383, "minutesAwake": 50, "minutesAfterWakeup": 4, "timeInBed": 433, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 65, "thirtyDayAvgMinutes": 38}, "wake": {"count": 23, "minutes": 50, "thirtyDayAvgMinutes": 45}, "light": {"count": 22, "minutes": 232, "thirtyDayAvgMinutes": 235}, "rem": {"count": 6, "minutes": 86, "thirtyDayAvgMinutes": 57}}, "data": [{"dateTime": "2020-07-23T00:32:00.000", "level": "wake", "seconds": 270}, {"dateTime": "2020-07-23T00:36:30.000", "level": "light", "seconds": 1230}, {"dateTime": "2020-07-23T00:57:00.000", "level": "rem", "seconds": 450}, {"dateTime": "2020-07-23T01:04:30.000", "level": "light", "seconds": 210}, {"dateTime": "2020-07-23T01:08:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-23T01:13:00.000", "level": "light", "seconds": 1890}, {"dateTime": "2020-07-23T01:44:30.000", "level": "deep", "seconds": 1200}, {"dateTime": "2020-07-23T02:04:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-07-23T02:08:00.000", "level": "light", "seconds": 1770}, {"dateTime": "2020-07-23T02:37:30.000", "level": "rem", "seconds": 2220}, {"dateTime": "2020-07-23T03:14:30.000", "level": "light", "seconds": 990}, {"dateTime": "2020-07-23T03:31:00.000", "level": "deep", "seconds": 780}, {"dateTime": "2020-07-23T03:44:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-07-23T03:48:00.000", "level": "light", "seconds": 2280}, {"dateTime": "2020-07-23T04:26:00.000", "level": "rem", "seconds": 1530}, {"dateTime": "2020-07-23T04:51:30.000", "level": "wake", "seconds": 510}, {"dateTime": "2020-07-23T05:00:00.000", "level": "light", "seconds": 3270}, {"dateTime": "2020-07-23T05:54:30.000", "level": "rem", "seconds": 1110}, {"dateTime": "2020-07-23T06:13:00.000", "level": "light", "seconds": 1140}, {"dateTime": "2020-07-23T06:32:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-23T06:38:00.000", "level": "light", "seconds": 1290}, {"dateTime": "2020-07-23T06:59:30.000", "level": "deep", "seconds": 1950}, {"dateTime": "2020-07-23T07:32:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-07-23T07:39:00.000", "level": "wake", "seconds": 390}], "shortData": [{"dateTime": "2020-07-23T01:31:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T02:17:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T03:14:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T03:50:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T03:58:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-23T04:00:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-23T04:05:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-23T04:11:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T04:35:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T05:16:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T05:42:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T06:07:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-23T06:17:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T06:28:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-23T07:31:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-23T07:36:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28138045645, "dateOfSleep": "2020-07-22", "startTime": "2020-07-22T00:47:00.000", "endTime": "2020-07-22T06:39:00.000", "duration": 21120000, "minutesToFallAsleep": 0, "minutesAsleep": 320, "minutesAwake": 32, "minutesAfterWakeup": 0, "timeInBed": 352, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 16, "thirtyDayAvgMinutes": 40}, "wake": {"count": 21, "minutes": 32, "thirtyDayAvgMinutes": 46}, "light": {"count": 21, "minutes": 255, "thirtyDayAvgMinutes": 233}, "rem": {"count": 4, "minutes": 49, "thirtyDayAvgMinutes": 58}}, "data": [{"dateTime": "2020-07-22T00:47:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-07-22T00:51:00.000", "level": "light", "seconds": 3240}, {"dateTime": "2020-07-22T01:45:00.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-07-22T01:50:30.000", "level": "light", "seconds": 570}, {"dateTime": "2020-07-22T02:00:00.000", "level": "rem", "seconds": 300}, {"dateTime": "2020-07-22T02:05:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-07-22T02:15:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-22T02:22:00.000", "level": "light", "seconds": 2820}, {"dateTime": "2020-07-22T03:09:00.000", "level": "rem", "seconds": 1050}, {"dateTime": "2020-07-22T03:26:30.000", "level": "light", "seconds": 6030}, {"dateTime": "2020-07-22T05:07:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-22T05:12:00.000", "level": "rem", "seconds": 1320}, {"dateTime": "2020-07-22T05:34:00.000", "level": "light", "seconds": 660}, {"dateTime": "2020-07-22T05:45:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-07-22T05:49:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-07-22T06:01:00.000", "level": "deep", "seconds": 960}, {"dateTime": "2020-07-22T06:17:00.000", "level": "light", "seconds": 1320}], "shortData": [{"dateTime": "2020-07-22T01:04:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T01:17:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-22T01:50:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T02:12:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T02:46:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T03:33:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-22T03:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T03:39:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T04:13:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-22T04:16:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T04:31:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-22T04:35:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-22T04:50:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-22T04:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T05:33:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-22T06:17:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-22T06:38:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28126833397, "dateOfSleep": "2020-07-21", "startTime": "2020-07-21T13:00:00.000", "endTime": "2020-07-21T14:01:30.000", "duration": 3660000, "minutesToFallAsleep": 0, "minutesAsleep": 54, "minutesAwake": 7, "minutesAfterWakeup": 0, "timeInBed": 61, "efficiency": 89, "type": "classic", "infoCode": 2, "levels": {"summary": {"restless": {"count": 3, "minutes": 7}, "awake": {"count": 0, "minutes": 0}, "asleep": {"count": 0, "minutes": 54}}, "data": [{"dateTime": "2020-07-21T13:00:00.000", "level": "asleep", "seconds": 120}, {"dateTime": "2020-07-21T13:02:00.000", "level": "restless", "seconds": 120}, {"dateTime": "2020-07-21T13:04:00.000", "level": "asleep", "seconds": 120}, {"dateTime": "2020-07-21T13:06:00.000", "level": "restless", "seconds": 120}, {"dateTime": "2020-07-21T13:08:00.000", "level": "asleep", "seconds": 2880}, {"dateTime": "2020-07-21T13:56:00.000", "level": "restless", "seconds": 180}, {"dateTime": "2020-07-21T13:59:00.000", "level": "asleep", "seconds": 60}, {"dateTime": "2020-07-21T14:00:00.000", "level": "restless", "seconds": 60}]}, "mainSleep": false}, {"logId": 28121576912, "dateOfSleep": "2020-07-21", "startTime": "2020-07-21T00:29:30.000", "endTime": "2020-07-21T06:35:30.000", "duration": 21960000, "minutesToFallAsleep": 0, "minutesAsleep": 313, "minutesAwake": 53, "minutesAfterWakeup": 1, "timeInBed": 366, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 5, "minutes": 47, "thirtyDayAvgMinutes": 39}, "wake": {"count": 25, "minutes": 53, "thirtyDayAvgMinutes": 46}, "light": {"count": 26, "minutes": 221, "thirtyDayAvgMinutes": 234}, "rem": {"count": 2, "minutes": 45, "thirtyDayAvgMinutes": 59}}, "data": [{"dateTime": "2020-07-21T00:29:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T00:30:00.000", "level": "light", "seconds": 1770}, {"dateTime": "2020-07-21T00:59:30.000", "level": "deep", "seconds": 870}, {"dateTime": "2020-07-21T01:14:00.000", "level": "light", "seconds": 2010}, {"dateTime": "2020-07-21T01:47:30.000", "level": "wake", "seconds": 450}, {"dateTime": "2020-07-21T01:55:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-07-21T02:05:30.000", "level": "wake", "seconds": 870}, {"dateTime": "2020-07-21T02:20:00.000", "level": "light", "seconds": 450}, {"dateTime": "2020-07-21T02:27:30.000", "level": "rem", "seconds": 2130}, {"dateTime": "2020-07-21T03:03:00.000", "level": "light", "seconds": 1050}, {"dateTime": "2020-07-21T03:20:30.000", "level": "deep", "seconds": 450}, {"dateTime": "2020-07-21T03:28:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-07-21T03:38:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-07-21T03:44:30.000", "level": "light", "seconds": 1860}, {"dateTime": "2020-07-21T04:15:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-07-21T04:21:30.000", "level": "light", "seconds": 150}, {"dateTime": "2020-07-21T04:24:00.000", "level": "rem", "seconds": 630}, {"dateTime": "2020-07-21T04:34:30.000", "level": "light", "seconds": 5460}, {"dateTime": "2020-07-21T06:05:30.000", "level": "deep", "seconds": 900}, {"dateTime": "2020-07-21T06:20:30.000", "level": "light", "seconds": 390}, {"dateTime": "2020-07-21T06:27:00.000", "level": "wake", "seconds": 510}], "shortData": [{"dateTime": "2020-07-21T00:35:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T00:42:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-21T01:13:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-21T01:17:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T01:29:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-21T01:32:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T03:02:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-21T03:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T03:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T03:54:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T04:20:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-21T04:35:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T04:38:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-21T04:48:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T05:09:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T05:12:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-21T05:15:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-21T05:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-21T05:43:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-21T05:55:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-21T06:20:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28109025691, "dateOfSleep": "2020-07-20", "startTime": "2020-07-20T01:46:00.000", "endTime": "2020-07-20T06:44:00.000", "duration": 17880000, "minutesToFallAsleep": 0, "minutesAsleep": 263, "minutesAwake": 35, "minutesAfterWakeup": 0, "timeInBed": 298, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 24, "thirtyDayAvgMinutes": 41}, "wake": {"count": 12, "minutes": 35, "thirtyDayAvgMinutes": 47}, "light": {"count": 15, "minutes": 197, "thirtyDayAvgMinutes": 239}, "rem": {"count": 4, "minutes": 42, "thirtyDayAvgMinutes": 62}}, "data": [{"dateTime": "2020-07-20T01:46:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-20T01:52:00.000", "level": "light", "seconds": 2790}, {"dateTime": "2020-07-20T02:38:30.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-20T02:45:00.000", "level": "light", "seconds": 1590}, {"dateTime": "2020-07-20T03:11:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-07-20T03:16:30.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-07-20T03:22:30.000", "level": "wake", "seconds": 510}, {"dateTime": "2020-07-20T03:31:00.000", "level": "light", "seconds": 2280}, {"dateTime": "2020-07-20T04:09:00.000", "level": "deep", "seconds": 270}, {"dateTime": "2020-07-20T04:13:30.000", "level": "light", "seconds": 900}, {"dateTime": "2020-07-20T04:28:30.000", "level": "rem", "seconds": 300}, {"dateTime": "2020-07-20T04:33:30.000", "level": "light", "seconds": 540}, {"dateTime": "2020-07-20T04:42:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-07-20T04:48:00.000", "level": "light", "seconds": 600}, {"dateTime": "2020-07-20T04:58:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-20T05:04:30.000", "level": "light", "seconds": 2190}, {"dateTime": "2020-07-20T05:41:00.000", "level": "rem", "seconds": 1560}, {"dateTime": "2020-07-20T06:07:00.000", "level": "light", "seconds": 1380}, {"dateTime": "2020-07-20T06:30:00.000", "level": "deep", "seconds": 840}], "shortData": [{"dateTime": "2020-07-20T02:48:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-20T02:53:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-20T03:01:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-20T04:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-20T05:06:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-20T05:15:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-20T05:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-20T06:06:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28084585007, "dateOfSleep": "2020-07-18", "startTime": "2020-07-18T00:28:00.000", "endTime": "2020-07-18T08:09:30.000", "duration": 27660000, "minutesToFallAsleep": 0, "minutesAsleep": 415, "minutesAwake": 46, "minutesAfterWakeup": 0, "timeInBed": 461, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 87, "thirtyDayAvgMinutes": 35}, "wake": {"count": 20, "minutes": 46, "thirtyDayAvgMinutes": 47}, "light": {"count": 23, "minutes": 229, "thirtyDayAvgMinutes": 241}, "rem": {"count": 5, "minutes": 99, "thirtyDayAvgMinutes": 56}}, "data": [{"dateTime": "2020-07-18T00:28:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-18T00:33:00.000", "level": "light", "seconds": 1020}, {"dateTime": "2020-07-18T00:50:00.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-07-18T00:56:00.000", "level": "light", "seconds": 1440}, {"dateTime": "2020-07-18T01:20:00.000", "level": "deep", "seconds": 2010}, {"dateTime": "2020-07-18T01:53:30.000", "level": "light", "seconds": 510}, {"dateTime": "2020-07-18T02:02:00.000", "level": "deep", "seconds": 2550}, {"dateTime": "2020-07-18T02:44:30.000", "level": "wake", "seconds": 480}, {"dateTime": "2020-07-18T02:52:30.000", "level": "light", "seconds": 2130}, {"dateTime": "2020-07-18T03:28:00.000", "level": "rem", "seconds": 2970}, {"dateTime": "2020-07-18T04:17:30.000", "level": "light", "seconds": 3120}, {"dateTime": "2020-07-18T05:09:30.000", "level": "rem", "seconds": 450}, {"dateTime": "2020-07-18T05:17:00.000", "level": "wake", "seconds": 720}, {"dateTime": "2020-07-18T05:29:00.000", "level": "light", "seconds": 2040}, {"dateTime": "2020-07-18T06:03:00.000", "level": "rem", "seconds": 1800}, {"dateTime": "2020-07-18T06:33:00.000", "level": "light", "seconds": 510}, {"dateTime": "2020-07-18T06:41:30.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-07-18T06:45:00.000", "level": "light", "seconds": 90}, {"dateTime": "2020-07-18T06:46:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-07-18T06:52:00.000", "level": "light", "seconds": 2190}, {"dateTime": "2020-07-18T07:28:30.000", "level": "deep", "seconds": 420}, {"dateTime": "2020-07-18T07:35:30.000", "level": "light", "seconds": 360}, {"dateTime": "2020-07-18T07:41:30.000", "level": "rem", "seconds": 420}, {"dateTime": "2020-07-18T07:48:30.000", "level": "light", "seconds": 1260}], "shortData": [{"dateTime": "2020-07-18T00:38:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-18T00:55:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-18T00:59:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-18T01:52:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-18T01:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-18T03:05:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-18T03:13:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-18T04:19:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-18T05:34:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-18T05:40:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-18T06:37:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-18T06:53:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-18T07:35:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-18T07:50:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-18T07:58:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-18T08:07:00.000", "level": "wake", "seconds": 150}]}, "mainSleep": true}, {"logId": 28071958752, "dateOfSleep": "2020-07-17", "startTime": "2020-07-16T23:41:00.000", "endTime": "2020-07-17T06:13:00.000", "duration": 23520000, "minutesToFallAsleep": 0, "minutesAsleep": 344, "minutesAwake": 48, "minutesAfterWakeup": 1, "timeInBed": 392, "efficiency": 91, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 22, "thirtyDayAvgMinutes": 37}, "wake": {"count": 14, "minutes": 48, "thirtyDayAvgMinutes": 47}, "light": {"count": 14, "minutes": 261, "thirtyDayAvgMinutes": 237}, "rem": {"count": 3, "minutes": 61, "thirtyDayAvgMinutes": 56}}, "data": [{"dateTime": "2020-07-16T23:41:00.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-07-16T23:50:00.000", "level": "light", "seconds": 510}, {"dateTime": "2020-07-16T23:58:30.000", "level": "deep", "seconds": 1290}, {"dateTime": "2020-07-17T00:20:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-17T00:25:00.000", "level": "light", "seconds": 300}, {"dateTime": "2020-07-17T00:30:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-17T00:36:00.000", "level": "light", "seconds": 4500}, {"dateTime": "2020-07-17T01:51:00.000", "level": "rem", "seconds": 660}, {"dateTime": "2020-07-17T02:02:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-17T02:07:00.000", "level": "light", "seconds": 5070}, {"dateTime": "2020-07-17T03:31:30.000", "level": "rem", "seconds": 1710}, {"dateTime": "2020-07-17T04:00:00.000", "level": "light", "seconds": 3840}, {"dateTime": "2020-07-17T05:04:00.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-07-17T05:14:30.000", "level": "light", "seconds": 1890}, {"dateTime": "2020-07-17T05:46:00.000", "level": "rem", "seconds": 1290}, {"dateTime": "2020-07-17T06:07:30.000", "level": "wake", "seconds": 330}], "shortData": [{"dateTime": "2020-07-17T01:06:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-17T01:29:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-17T02:12:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-17T02:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-17T03:11:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-17T04:30:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-17T05:00:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-17T05:27:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28057143995, "dateOfSleep": "2020-07-16", "startTime": "2020-07-15T23:24:30.000", "endTime": "2020-07-16T07:11:30.000", "duration": 28020000, "minutesToFallAsleep": 0, "minutesAsleep": 414, "minutesAwake": 53, "minutesAfterWakeup": 0, "timeInBed": 467, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 61, "thirtyDayAvgMinutes": 32}, "wake": {"count": 19, "minutes": 53, "thirtyDayAvgMinutes": 46}, "light": {"count": 23, "minutes": 277, "thirtyDayAvgMinutes": 229}, "rem": {"count": 5, "minutes": 76, "thirtyDayAvgMinutes": 51}}, "data": [{"dateTime": "2020-07-15T23:24:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-15T23:25:00.000", "level": "light", "seconds": 1410}, {"dateTime": "2020-07-15T23:48:30.000", "level": "deep", "seconds": 3420}, {"dateTime": "2020-07-16T00:45:30.000", "level": "light", "seconds": 3390}, {"dateTime": "2020-07-16T01:42:00.000", "level": "rem", "seconds": 870}, {"dateTime": "2020-07-16T01:56:30.000", "level": "light", "seconds": 330}, {"dateTime": "2020-07-16T02:02:00.000", "level": "rem", "seconds": 480}, {"dateTime": "2020-07-16T02:10:00.000", "level": "light", "seconds": 60}, {"dateTime": "2020-07-16T02:11:00.000", "level": "wake", "seconds": 780}, {"dateTime": "2020-07-16T02:24:00.000", "level": "light", "seconds": 1440}, {"dateTime": "2020-07-16T02:48:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-07-16T02:52:00.000", "level": "light", "seconds": 1080}, {"dateTime": "2020-07-16T03:10:00.000", "level": "rem", "seconds": 450}, {"dateTime": "2020-07-16T03:17:30.000", "level": "light", "seconds": 120}, {"dateTime": "2020-07-16T03:19:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-16T03:25:00.000", "level": "light", "seconds": 1980}, {"dateTime": "2020-07-16T03:58:00.000", "level": "rem", "seconds": 1200}, {"dateTime": "2020-07-16T04:18:00.000", "level": "light", "seconds": 3180}, {"dateTime": "2020-07-16T05:11:00.000", "level": "deep", "seconds": 480}, {"dateTime": "2020-07-16T05:19:00.000", "level": "light", "seconds": 510}, {"dateTime": "2020-07-16T05:27:30.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-07-16T05:34:30.000", "level": "light", "seconds": 2130}, {"dateTime": "2020-07-16T06:10:00.000", "level": "rem", "seconds": 1530}, {"dateTime": "2020-07-16T06:35:30.000", "level": "light", "seconds": 1440}, {"dateTime": "2020-07-16T06:59:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-16T07:05:00.000", "level": "light", "seconds": 390}], "shortData": [{"dateTime": "2020-07-15T23:48:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-16T00:45:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-16T01:33:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-16T01:36:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-16T02:55:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-16T04:26:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-16T04:44:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-16T05:23:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-16T06:44:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-16T06:50:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-16T06:53:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-16T07:08:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-16T07:10:30.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28041256867, "dateOfSleep": "2020-07-15", "startTime": "2020-07-15T01:52:00.000", "endTime": "2020-07-15T06:33:30.000", "duration": 16860000, "minutesToFallAsleep": 0, "minutesAsleep": 242, "minutesAwake": 39, "minutesAfterWakeup": 5, "timeInBed": 281, "efficiency": 93, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 25, "thirtyDayAvgMinutes": 34}, "wake": {"count": 14, "minutes": 39, "thirtyDayAvgMinutes": 48}, "light": {"count": 16, "minutes": 197, "thirtyDayAvgMinutes": 237}, "rem": {"count": 3, "minutes": 20, "thirtyDayAvgMinutes": 59}}, "data": [{"dateTime": "2020-07-15T01:52:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-07-15T01:56:00.000", "level": "light", "seconds": 6030}, {"dateTime": "2020-07-15T03:36:30.000", "level": "deep", "seconds": 540}, {"dateTime": "2020-07-15T03:45:30.000", "level": "light", "seconds": 1410}, {"dateTime": "2020-07-15T04:09:00.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-07-15T04:15:00.000", "level": "light", "seconds": 2010}, {"dateTime": "2020-07-15T04:48:30.000", "level": "deep", "seconds": 930}, {"dateTime": "2020-07-15T05:04:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-07-15T05:11:00.000", "level": "rem", "seconds": 930}, {"dateTime": "2020-07-15T05:26:30.000", "level": "light", "seconds": 30}, {"dateTime": "2020-07-15T05:27:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-15T05:33:30.000", "level": "light", "seconds": 1470}, {"dateTime": "2020-07-15T05:58:00.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-07-15T06:07:00.000", "level": "light", "seconds": 1110}, {"dateTime": "2020-07-15T06:25:30.000", "level": "wake", "seconds": 480}], "shortData": [{"dateTime": "2020-07-15T02:26:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-15T03:02:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-15T03:18:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-15T04:02:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-15T04:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-15T05:06:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-15T05:23:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-15T05:42:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-15T06:09:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-15T06:16:30.000", "level": "wake", "seconds": 90}]}, "mainSleep": true}, {"logId": 28034829992, "dateOfSleep": "2020-07-14", "startTime": "2020-07-14T17:11:00.000", "endTime": "2020-07-14T18:38:00.000", "duration": 5220000, "minutesToFallAsleep": 0, "minutesAsleep": 80, "minutesAwake": 7, "minutesAfterWakeup": 0, "timeInBed": 87, "efficiency": 92, "type": "classic", "infoCode": 2, "levels": {"summary": {"restless": {"count": 3, "minutes": 7}, "awake": {"count": 0, "minutes": 0}, "asleep": {"count": 0, "minutes": 80}}, "data": [{"dateTime": "2020-07-14T17:11:00.000", "level": "asleep", "seconds": 120}, {"dateTime": "2020-07-14T17:13:00.000", "level": "restless", "seconds": 300}, {"dateTime": "2020-07-14T17:18:00.000", "level": "asleep", "seconds": 60}, {"dateTime": "2020-07-14T17:19:00.000", "level": "restless", "seconds": 60}, {"dateTime": "2020-07-14T17:20:00.000", "level": "asleep", "seconds": 4440}, {"dateTime": "2020-07-14T18:34:00.000", "level": "restless", "seconds": 60}, {"dateTime": "2020-07-14T18:35:00.000", "level": "asleep", "seconds": 180}]}, "mainSleep": false}, {"logId": 28025635135, "dateOfSleep": "2020-07-14", "startTime": "2020-07-14T00:31:00.000", "endTime": "2020-07-14T07:16:30.000", "duration": 24300000, "minutesToFallAsleep": 0, "minutesAsleep": 351, "minutesAwake": 54, "minutesAfterWakeup": 0, "timeInBed": 405, "efficiency": 93, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 30, "thirtyDayAvgMinutes": 35}, "wake": {"count": 21, "minutes": 54, "thirtyDayAvgMinutes": 45}, "light": {"count": 22, "minutes": 257, "thirtyDayAvgMinutes": 231}, "rem": {"count": 4, "minutes": 64, "thirtyDayAvgMinutes": 58}}, "data": [{"dateTime": "2020-07-14T00:31:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-14T00:36:00.000", "level": "light", "seconds": 4350}, {"dateTime": "2020-07-14T01:48:30.000", "level": "rem", "seconds": 1140}, {"dateTime": "2020-07-14T02:07:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-14T02:13:00.000", "level": "light", "seconds": 1050}, {"dateTime": "2020-07-14T02:30:30.000", "level": "deep", "seconds": 1440}, {"dateTime": "2020-07-14T02:54:30.000", "level": "light", "seconds": 930}, {"dateTime": "2020-07-14T03:10:00.000", "level": "rem", "seconds": 1800}, {"dateTime": "2020-07-14T03:40:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-14T03:46:00.000", "level": "light", "seconds": 1800}, {"dateTime": "2020-07-14T04:16:00.000", "level": "deep", "seconds": 420}, {"dateTime": "2020-07-14T04:23:00.000", "level": "light", "seconds": 5940}, {"dateTime": "2020-07-14T06:02:00.000", "level": "rem", "seconds": 540}, {"dateTime": "2020-07-14T06:11:00.000", "level": "light", "seconds": 990}, {"dateTime": "2020-07-14T06:27:30.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-07-14T06:33:30.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-07-14T06:43:00.000", "level": "light", "seconds": 390}, {"dateTime": "2020-07-14T06:49:30.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-07-14T06:54:30.000", "level": "light", "seconds": 840}, {"dateTime": "2020-07-14T07:08:30.000", "level": "wake", "seconds": 480}], "shortData": [{"dateTime": "2020-07-14T00:50:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-14T00:56:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-14T02:53:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-14T02:56:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-14T04:51:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-14T04:58:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-14T05:03:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-14T05:07:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-14T05:13:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-14T05:40:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-14T05:45:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-14T05:49:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-14T06:45:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-14T07:00:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-14T07:04:00.000", "level": "wake", "seconds": 120}]}, "mainSleep": true}, {"logId": 28013058236, "dateOfSleep": "2020-07-13", "startTime": "2020-07-13T00:40:00.000", "endTime": "2020-07-13T07:17:30.000", "duration": 23820000, "minutesToFallAsleep": 0, "minutesAsleep": 342, "minutesAwake": 55, "minutesAfterWakeup": 0, "timeInBed": 397, "efficiency": 95, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 45, "thirtyDayAvgMinutes": 30}, "wake": {"count": 25, "minutes": 55, "thirtyDayAvgMinutes": 41}, "light": {"count": 27, "minutes": 244, "thirtyDayAvgMinutes": 224}, "rem": {"count": 5, "minutes": 53, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-07-13T00:40:00.000", "level": "wake", "seconds": 870}, {"dateTime": "2020-07-13T00:54:30.000", "level": "light", "seconds": 270}, {"dateTime": "2020-07-13T00:59:00.000", "level": "deep", "seconds": 1830}, {"dateTime": "2020-07-13T01:29:30.000", "level": "light", "seconds": 900}, {"dateTime": "2020-07-13T01:44:30.000", "level": "rem", "seconds": 1080}, {"dateTime": "2020-07-13T02:02:30.000", "level": "light", "seconds": 600}, {"dateTime": "2020-07-13T02:12:30.000", "level": "rem", "seconds": 450}, {"dateTime": "2020-07-13T02:20:00.000", "level": "light", "seconds": 1080}, {"dateTime": "2020-07-13T02:38:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-13T02:44:00.000", "level": "light", "seconds": 1620}, {"dateTime": "2020-07-13T03:11:00.000", "level": "rem", "seconds": 960}, {"dateTime": "2020-07-13T03:27:00.000", "level": "light", "seconds": 2430}, {"dateTime": "2020-07-13T04:07:30.000", "level": "deep", "seconds": 930}, {"dateTime": "2020-07-13T04:23:00.000", "level": "light", "seconds": 1590}, {"dateTime": "2020-07-13T04:49:30.000", "level": "rem", "seconds": 300}, {"dateTime": "2020-07-13T04:54:30.000", "level": "light", "seconds": 1530}, {"dateTime": "2020-07-13T05:20:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-07-13T05:26:00.000", "level": "light", "seconds": 180}, {"dateTime": "2020-07-13T05:29:00.000", "level": "wake", "seconds": 390}, {"dateTime": "2020-07-13T05:35:30.000", "level": "light", "seconds": 5430}, {"dateTime": "2020-07-13T07:06:00.000", "level": "rem", "seconds": 420}, {"dateTime": "2020-07-13T07:13:00.000", "level": "wake", "seconds": 270}], "shortData": [{"dateTime": "2020-07-13T00:59:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T02:02:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T02:04:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T02:06:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T02:27:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-13T02:31:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T03:01:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T03:38:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-13T04:22:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T04:25:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-13T04:45:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-13T04:58:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-13T05:02:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-13T05:17:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T05:41:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T05:55:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T06:06:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-13T06:11:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-07-13T06:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-13T07:03:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 27999076911, "dateOfSleep": "2020-07-12", "startTime": "2020-07-12T01:34:30.000", "endTime": "2020-07-12T07:34:30.000", "duration": 21600000, "minutesToFallAsleep": 0, "minutesAsleep": 315, "minutesAwake": 45, "minutesAfterWakeup": 0, "timeInBed": 360, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 27, "thirtyDayAvgMinutes": 33}, "wake": {"count": 18, "minutes": 45, "thirtyDayAvgMinutes": 36}, "light": {"count": 19, "minutes": 228, "thirtyDayAvgMinutes": 220}, "rem": {"count": 2, "minutes": 60, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-07-12T01:34:30.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-07-12T01:41:30.000", "level": "light", "seconds": 4950}, {"dateTime": "2020-07-12T03:04:00.000", "level": "rem", "seconds": 1050}, {"dateTime": "2020-07-12T03:21:30.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-07-12T03:31:00.000", "level": "light", "seconds": 990}, {"dateTime": "2020-07-12T03:47:30.000", "level": "wake", "seconds": 750}, {"dateTime": "2020-07-12T04:00:00.000", "level": "light", "seconds": 1590}, {"dateTime": "2020-07-12T04:26:30.000", "level": "rem", "seconds": 2520}, {"dateTime": "2020-07-12T05:08:30.000", "level": "light", "seconds": 1740}, {"dateTime": "2020-07-12T05:37:30.000", "level": "deep", "seconds": 1410}, {"dateTime": "2020-07-12T06:01:00.000", "level": "light", "seconds": 2880}, {"dateTime": "2020-07-12T06:49:00.000", "level": "deep", "seconds": 270}, {"dateTime": "2020-07-12T06:53:30.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-07-12T07:13:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-07-12T07:17:00.000", "level": "light", "seconds": 1050}], "shortData": [{"dateTime": "2020-07-12T02:06:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T02:34:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T05:11:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-12T05:15:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-12T06:00:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T06:05:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-12T06:14:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T06:22:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T06:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T06:32:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T06:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T07:19:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-12T07:26:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-12T07:32:00.000", "level": "wake", "seconds": 150}]}, "mainSleep": true}, {"logId": 27986571865, "dateOfSleep": "2020-07-11", "startTime": "2020-07-11T01:44:00.000", "endTime": "2020-07-11T07:33:00.000", "duration": 20940000, "minutesToFallAsleep": 0, "minutesAsleep": 313, "minutesAwake": 36, "minutesAfterWakeup": 0, "timeInBed": 349, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 33, "thirtyDayAvgMinutes": 0}, "wake": {"count": 21, "minutes": 36, "thirtyDayAvgMinutes": 0}, "light": {"count": 21, "minutes": 220, "thirtyDayAvgMinutes": 0}, "rem": {"count": 5, "minutes": 60, "thirtyDayAvgMinutes": 0}}, "data": [{"dateTime": "2020-07-11T01:44:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T01:44:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-07-11T01:51:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-07-11T01:57:00.000", "level": "light", "seconds": 240}, {"dateTime": "2020-07-11T02:01:00.000", "level": "deep", "seconds": 1140}, {"dateTime": "2020-07-11T02:20:00.000", "level": "light", "seconds": 480}, {"dateTime": "2020-07-11T02:28:00.000", "level": "rem", "seconds": 1620}, {"dateTime": "2020-07-11T02:55:00.000", "level": "light", "seconds": 2340}, {"dateTime": "2020-07-11T03:34:00.000", "level": "deep", "seconds": 450}, {"dateTime": "2020-07-11T03:41:30.000", "level": "light", "seconds": 3150}, {"dateTime": "2020-07-11T04:34:00.000", "level": "rem", "seconds": 1200}, {"dateTime": "2020-07-11T04:54:00.000", "level": "wake", "seconds": 630}, {"dateTime": "2020-07-11T05:04:30.000", "level": "light", "seconds": 660}, {"dateTime": "2020-07-11T05:15:30.000", "level": "deep", "seconds": 450}, {"dateTime": "2020-07-11T05:23:00.000", "level": "light", "seconds": 6990}, {"dateTime": "2020-07-11T07:19:30.000", "level": "rem", "seconds": 810}], "shortData": [{"dateTime": "2020-07-11T01:44:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-11T01:59:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T02:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T03:06:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-07-11T03:41:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T04:02:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-11T04:19:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-11T05:30:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-07-11T05:55:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-11T06:05:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-11T06:12:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-07-11T06:31:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T06:53:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T07:01:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T07:07:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T07:12:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-07-11T07:16:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T07:25:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-07-11T07:27:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}]