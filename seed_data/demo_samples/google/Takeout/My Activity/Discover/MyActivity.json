[{"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Partly cloudy"}], "time": "2021-04-18T17:53:15.905Z", "products": ["Discover"]}, {"header": "Discover", "title": "67 cards in your feed", "subtitles": [{"name": "Ad from autonomous.ai - viewed, clicked"}, {"name": "Ad from clearbit.com - viewed"}, {"name": "Ad from contentstack.com"}, {"name": "Ad from tableau.com - viewed"}, {"name": "63 other cards"}], "time": "2021-04-18T01:55:29.106Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "5° warmer than today · See the full forecast - dismissed"}], "time": "2021-04-18T01:12:32.288Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS -1.75%<font color='#ea4335'>▼</font> TSLA +0.13%<font color='#1e8e3e'>▲</font> · Tap for more details"}, {"name": "67° / 47° · Mostly cloudy · See the full forecast - dismissed"}], "time": "2021-04-17T03:23:59.124Z", "products": ["Discover"]}, {"header": "Discover", "title": "17 cards in your feed", "subtitles": [{"name": "Ad from tableau.com"}, {"name": "16 other cards"}], "time": "2021-04-17T00:21:18.894Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS +1.83%<font color='#1e8e3e'>▲</font> TSLA +0.90%<font color='#1e8e3e'>▲</font> · Tap for more details"}, {"name": "69° / 45° · Mostly sunny · See the full forecast"}], "time": "2021-04-16T03:33:38.810Z", "products": ["Discover"]}, {"header": "Discover", "title": "17 cards in your feed", "subtitles": [{"name": "Ad from tableau.com"}, {"name": "16 other cards"}], "time": "2021-04-15T16:27:25.033Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -3.95%<font color='#ea4335'>▼</font> ZS -2.17%<font color='#ea4335'>▼</font> · Tap for more details"}, {"name": "11° cooler than today · See the full forecast"}], "time": "2021-04-15T03:29:42.150Z", "products": ["Discover"]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from kickstarter.com"}, {"name": "10 other cards"}], "time": "2021-04-15T01:16:38.987Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "18 cards in your feed", "subtitles": [{"name": "Ad from onepeloton.com - viewed"}, {"name": "17 other cards"}], "time": "2021-04-14T03:03:37.140Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +8.60%<font color='#1e8e3e'>▲</font> ZS +4.39%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "8° warmer than today · See the full forecast - dismissed"}], "time": "2021-04-14T02:39:25.085Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "5° cooler than today · See the full forecast - dismissed"}], "time": "2021-04-13T03:29:10.293Z", "products": ["Discover"]}, {"header": "Discover", "title": "18 cards in your feed", "subtitles": [{"name": "Ad from kickstarter.com"}, {"name": "17 other cards"}], "time": "2021-04-12T11:20:23.742Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "74 cards in your feed", "subtitles": [{"name": "Ad from tableau.com - viewed"}, {"name": "Ad from yellowfinbi.com"}, {"name": "72 other cards"}], "time": "2021-04-12T02:24:50.726Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "4° cooler than today · See the full forecast - dismissed"}], "time": "2021-04-12T00:36:14.521Z", "products": ["Discover"]}, {"header": "Discover", "title": "39 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Ad from wpengine.com"}, {"name": "36 other cards"}], "time": "2021-04-11T03:39:14.674Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.949975,-79.098395&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.843089,-78.645547&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "79° / 52° · Partly cloudy · See the full forecast"}], "time": "2021-04-11T03:37:59.759Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -0.99%<font color='#ea4335'>▼</font> ZS -0.08%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "80° / 62° · Isolated thunderstorms · See the full forecast - dismissed"}], "time": "2021-04-10T03:15:38.902Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS +2.87%<font color='#1e8e3e'>▲</font> TSLA +1.91%<font color='#1e8e3e'>▲</font> · Tap for more details"}, {"name": "82° / 58° · Partly cloudy · See the full forecast"}], "time": "2021-04-09T03:04:33.310Z", "products": ["Discover"]}, {"header": "Discover", "title": "106 cards in your feed", "subtitles": [{"name": "Ad from lse.ac.uk - viewed"}, {"name": "Ad from on24.com - viewed"}, {"name": "Ad from superside.com - viewed"}, {"name": "Ad from videos4world.com - viewed"}, {"name": "Ad from wsj.com - viewed"}, {"name": "101 other cards"}], "time": "2021-04-08T03:20:34.912Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -2.99%<font color='#ea4335'>▼</font> ZS -1.15%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "6° cooler than today · See the full forecast - dismissed"}], "time": "2021-04-08T02:42:05.510Z", "products": ["Discover"]}, {"header": "Discover", "title": "57 cards in your feed", "subtitles": [{"name": "Ad from dell.com - viewed"}, {"name": "Ad from kickstarter.com"}, {"name": "Ad from pluralsight.com - viewed"}, {"name": "Ad from topstep.com"}, {"name": "53 other cards"}], "time": "2021-04-07T01:20:53.638Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS +3.96%<font color='#1e8e3e'>▲</font> TSLA +0.08%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "84° / 55° · Mostly sunny · See the full forecast - dismissed"}], "time": "2021-04-07T01:20:47.836Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +4.43%<font color='#1e8e3e'>▲</font> ZS -0.33%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "4° warmer than today · See the full forecast - dismissed"}], "time": "2021-04-06T03:56:46.240Z", "products": ["Discover"]}, {"header": "Discover", "title": "29 cards in your feed", "subtitles": [{"name": "Ad from benevity.com - viewed"}, {"name": "Ad from kickstarter.com"}, {"name": "27 other cards"}], "time": "2021-04-06T00:10:11.702Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "64 cards in your feed", "subtitles": [{"name": "Ad from jhu.edu"}, {"name": "Ad from kickstarter.com - viewed"}, {"name": "Ad from mimecast.com - viewed"}, {"name": "61 other cards"}], "time": "2021-04-05T03:04:58.750Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.779445,-81.997098&zoom=10", "source": "Based on your past activity"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "75° / 51° · Sunny · See the full forecast - dismissed"}], "time": "2021-04-05T02:49:01.436Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "14° warmer than today · See the full forecast - dismissed"}], "time": "2021-04-04T03:24:00.682Z", "products": ["Discover"]}, {"header": "Discover", "title": "134 cards in your feed", "subtitles": [{"name": "Ad from alarm.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from splunk.com - viewed"}, {"name": "Ad from veeam.com"}, {"name": "130 other cards"}], "time": "2021-04-04T02:24:09.072Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.779445,-81.997098&zoom=10", "source": "Based on your past activity"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=10", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "137 cards in your feed", "subtitles": [{"name": "Ad from mimecast.com - viewed"}, {"name": "Ad from mode.com - viewed"}, {"name": "135 other cards"}], "time": "2021-04-03T03:11:37.411Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=10", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "54 cards in your feed", "subtitles": [{"name": "Ad from alarm.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from utexas.edu"}, {"name": "51 other cards"}], "time": "2021-04-02T01:29:24.300Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.615480,-82.312629&zoom=8", "source": "Based on your past activity"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.753392,-81.931657&zoom=9", "source": "Based on your past activity"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=10", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "50 cards in your feed", "subtitles": [{"name": "Ad from gartner.com - viewed"}, {"name": "Ad from kickstarter.com"}, {"name": "Ad from mit.edu"}, {"name": "47 other cards"}], "time": "2021-04-01T01:41:04.657Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.779445,-81.997098&zoom=8", "source": "Based on your past activity"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.779445,-81.997098&zoom=10", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=10", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "36 cards in your feed", "subtitles": [{"name": "Ad from powerhome.com"}, {"name": "Ad from tableau.com"}, {"name": "Ad from thetradedesk.com - viewed"}, {"name": "33 other cards"}], "time": "2021-03-31T03:20:34.497Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.779445,-81.997098&zoom=10", "source": "Based on your past activity"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.779445,-81.997098&zoom=10", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.745469,-81.915290&zoom=11", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}]}, {"header": "Discover", "title": "40 cards in your feed", "subtitles": [{"name": "Ad from benevity.com - viewed"}, {"name": "Ad from cornell.edu - viewed"}, {"name": "Ad from mit.edu"}, {"name": "Ad from pluralsight.com - viewed"}, {"name": "36 other cards"}], "time": "2021-03-29T01:04:56.009Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "119 cards in your feed", "subtitles": [{"name": "Ad from experiencemastercraft.com"}, {"name": "Ad from kachava.com - viewed"}, {"name": "Ad from mit.edu - viewed"}, {"name": "Ad from yieldstreet.com - viewed"}, {"name": "115 other cards"}], "time": "2021-03-28T03:51:01.040Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "60 cards in your feed", "subtitles": [{"name": "Ad from cornell.edu - viewed"}, {"name": "Ad from mudwtr.com - viewed"}, {"name": "58 other cards"}], "time": "2021-03-26T11:41:48.068Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "45 cards in your feed", "subtitles": [{"name": "Ad from polestar.com"}, {"name": "Ad from tableau.com - viewed"}, {"name": "Ad from utexas.edu"}, {"name": "42 other cards"}], "time": "2021-03-25T03:56:37.342Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "76 cards in your feed", "subtitles": [{"name": "Ad from clearbit.com"}, {"name": "Ad from fireeye.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "73 other cards"}], "time": "2021-03-24T03:02:01.907Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from mode.com"}, {"name": "10 other cards"}], "time": "2021-03-22T11:51:56.941Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "108 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "Ad from mit.edu"}, {"name": "Ad from mode.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "103 other cards"}], "time": "2021-03-22T03:22:01.670Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "72 cards in your feed", "subtitles": [{"name": "Ad from algorithmicschool.com"}, {"name": "Ad from mit.edu"}, {"name": "Ad from mode.com"}, {"name": "Ad from on24.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from wpengine.com"}, {"name": "66 other cards"}], "time": "2021-03-20T22:56:01.192Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.883969,-78.622850&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}]}, {"header": "Discover", "title": "77 cards in your feed", "subtitles": [{"name": "Ad from on24.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "Weather"}, {"name": "74 other cards"}], "time": "2021-03-20T03:27:01.203Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.941960,-78.554728&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}]}, {"header": "Discover", "title": "41 cards in your feed", "subtitles": [{"name": "Ad from chirpbooks.com"}, {"name": "Ad from nvidia.com"}, {"name": "Ad from rentalsunited.com"}, {"name": "Ad from superside.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "35 other cards"}], "time": "2021-03-18T03:43:35.693Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "70 cards in your feed", "subtitles": [{"name": "Ad from 1800accountant.com"}, {"name": "Ad from clearbit.com"}, {"name": "Ad from cornell.edu"}, {"name": "Ad from mit.edu"}, {"name": "Ad from northwestern.edu"}, {"name": "Ad from weissratings.com"}, {"name": "Weather"}, {"name": "63 other cards"}], "time": "2021-03-17T03:52:29.374Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "36 cards in your feed", "subtitles": [{"name": "Ad from 1800accountant.com"}, {"name": "Ad from northwestern.edu"}, {"name": "Ad from sas.com"}, {"name": "Weather"}, {"name": "32 other cards"}], "time": "2021-03-15T02:12:56.779Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "47 cards in your feed", "subtitles": [{"name": "Ad from arcticwolf.com"}, {"name": "Ad from nextinsurance.com"}, {"name": "Ad from northwestern.edu"}, {"name": "Ad from polestar.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "41 other cards"}], "time": "2021-03-13T22:53:07.565Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "38 cards in your feed", "subtitles": [{"name": "Ad from riministreet.com"}, {"name": "Ad from thetradedesk.com"}, {"name": "Weather"}, {"name": "35 other cards"}], "time": "2021-03-13T01:21:05.320Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "35 cards in your feed", "subtitles": [{"name": "Ad from nextinsurance.com"}, {"name": "Ad from yellowfinbi.com"}, {"name": "Weather"}, {"name": "32 other cards"}], "time": "2021-03-12T04:52:08.494Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.920459,-78.554728&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}]}, {"header": "Discover", "title": "18 cards in your feed", "subtitles": [{"name": "Ad from yellowfinbi.com"}, {"name": "Weather"}, {"name": "16 other cards"}], "time": "2021-03-10T22:46:32.514Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from sas.com"}, {"name": "Weather"}, {"name": "9 other cards"}], "time": "2021-03-09T22:40:17.052Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.843089,-78.645547&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}]}, {"header": "Discover", "title": "38 cards in your feed", "subtitles": [{"name": "Ad from clearbit.com"}, {"name": "Ad from hubilo.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "Ad from tableau.com"}, {"name": "Weather"}, {"name": "33 other cards"}], "time": "2021-03-09T04:19:42.960Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "27 cards in your feed", "subtitles": [{"name": "Ad from pluralsight.com"}, {"name": "Ad from riministreet.com"}, {"name": "Weather"}, {"name": "24 other cards"}], "time": "2021-03-08T01:27:37.836Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from contentstack.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "8 other cards"}], "time": "2021-03-05T23:28:55.205Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "58 cards in your feed", "subtitles": [{"name": "Ad from bauerspowerpicks.com"}, {"name": "Ad from ey.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from mrcooluniversal.com"}, {"name": "Ad from reelpaper.com"}, {"name": "Weather"}, {"name": "52 other cards"}], "time": "2021-03-05T02:33:45.825Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "40 cards in your feed", "subtitles": [{"name": "Ad from allhands.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Ad from zenefits.com"}, {"name": "Weather"}, {"name": "36 other cards"}], "time": "2021-03-04T00:32:10.612Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "36 cards in your feed", "subtitles": [{"name": "Ad from ge.com"}, {"name": "Ad from yellowfinbi.com"}, {"name": "Weather"}, {"name": "33 other cards"}], "time": "2021-03-03T04:01:56.528Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "57 cards in your feed", "subtitles": [{"name": "Ad from mit.edu"}, {"name": "Ad from mode.com"}, {"name": "Ad from sas.com"}, {"name": "Ad from zenefits.com"}, {"name": "Weather"}, {"name": "52 other cards"}], "time": "2021-03-02T04:13:07.411Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}]}, {"header": "Discover", "title": "89 cards in your feed", "subtitles": [{"name": "Ad from auvik.com"}, {"name": "Ad from tableau.com"}, {"name": "Ad from wsj.com"}, {"name": "86 other cards"}], "time": "2021-03-01T00:24:19.685Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.939772,-78.532231&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.999845,-78.486560&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from tableau.com"}, {"name": "10 other cards"}], "time": "2021-02-27T22:18:17.390Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "55 cards in your feed", "subtitles": [{"name": "Ad from leaffilter.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "53 other cards"}], "time": "2021-02-27T04:14:57.032Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "55 cards in your feed", "subtitles": [{"name": "Ad from gusto.com"}, {"name": "Ad from mit.edu"}, {"name": "Ad from vimeo.com"}, {"name": "52 other cards"}], "time": "2021-02-26T04:35:15.489Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "96 cards in your feed", "subtitles": [{"name": "Ad from elemis.com"}, {"name": "Ad from group-health-quotes.com"}, {"name": "Ad from gusto.com"}, {"name": "Ad from homeadvisor.com"}, {"name": "Ad from leaffilter.com"}, {"name": "91 other cards"}], "time": "2021-02-25T02:36:05.088Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "69 cards in your feed", "subtitles": [{"name": "Ad from braincorp.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from the-financialnews.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "65 other cards"}], "time": "2021-02-24T00:53:40.690Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.920459,-78.554728&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.941960,-78.554728&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "129 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Ad from mimecast.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "126 other cards"}], "time": "2021-02-23T03:25:45.845Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "122 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from kount.com"}, {"name": "Ad from medallia.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Ad from superside.com"}, {"name": "Ad from zenefits.com"}, {"name": "115 other cards"}], "time": "2021-02-22T03:44:32.831Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.920459,-78.554728&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}]}, {"header": "Discover", "title": "46 cards in your feed", "subtitles": [{"name": "Ad from bloomberg.com"}, {"name": "Ad from dataiku.com"}, {"name": "Ad from tableau.com"}, {"name": "Ad from zenefits.com"}, {"name": "42 other cards"}], "time": "2021-02-21T03:28:22.263Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.879642,-78.804282&zoom=12", "source": "From your Location History", "sourceUrl": "https://www.google.com/maps/timeline"}, {"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.949975,-79.098395&zoom=12", "source": "Based on your past activity"}]}, {"header": "Discover", "title": "90 cards in your feed", "subtitles": [{"name": "Ad from blockadvisors.com"}, {"name": "Ad from preply.com"}, {"name": "Ad from zenefits.com"}, {"name": "87 other cards"}], "time": "2021-02-20T03:08:40.594Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "35 cards in your feed", "subtitles": [{"name": "Ad from braincorp.com"}, {"name": "Ad from wsj.com"}, {"name": "33 other cards"}], "time": "2021-02-18T20:34:25.040Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from factor75.com"}, {"name": "10 other cards"}], "time": "2021-02-18T02:57:21.910Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "30 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Ad from muir-way.com"}, {"name": "Ad from wsj.com"}, {"name": "27 other cards"}], "time": "2021-02-17T01:34:08.783Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "59 cards in your feed", "subtitles": [{"name": "Ad from braincorp.com"}, {"name": "Ad from clearbit.com"}, {"name": "Ad from mimecast.com"}, {"name": "Ad from riministreet.com"}, {"name": "55 other cards"}], "time": "2021-02-16T03:15:54.735Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "132 cards in your feed", "subtitles": [{"name": "Ad from braincorp.com"}, {"name": "Ad from fireeye.com"}, {"name": "Ad from riministreet.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "128 other cards"}], "time": "2021-02-15T03:00:01.085Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "159 cards in your feed", "subtitles": [{"name": "Ad from homechef.com"}, {"name": "Ad from kount.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "156 other cards"}], "time": "2021-02-14T03:47:20.260Z", "products": ["Discover"], "locationInfos": [{"name": "At this general area", "url": "https://www.google.com/maps/@?api=1&map_action=map&center=35.944143,-78.577440&zoom=12", "source": "From your places (Home)", "sourceUrl": "https://support.google.com/maps/answer/3184808"}]}, {"header": "Discover", "title": "22 cards in your feed", "subtitles": [{"name": "Ad from fool.com"}, {"name": "21 other cards"}], "time": "2021-02-13T03:37:54.511Z", "products": ["Discover"]}, {"header": "Discover", "title": "77 cards in your feed", "subtitles": [{"name": "Ad from bloomberg.com"}, {"name": "Ad from wpengine.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "74 other cards"}], "time": "2021-02-11T21:42:33.588Z", "products": ["Discover"]}, {"header": "Discover", "title": "21 cards in your feed", "subtitles": [{"name": "Ad from factor75.com"}, {"name": "20 other cards"}], "time": "2021-02-11T03:24:21.325Z", "products": ["Discover"]}, {"header": "Discover", "title": "30 cards in your feed", "subtitles": [{"name": "Ad from tableau.com"}, {"name": "Ad from wsj.com"}, {"name": "28 other cards"}], "time": "2021-02-09T20:43:44.795Z", "products": ["Discover"]}, {"header": "Discover", "title": "28 cards in your feed", "subtitles": [{"name": "Ad from pluralsight.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "26 other cards"}], "time": "2021-02-09T00:37:15.572Z", "products": ["Discover"]}, {"header": "Discover", "title": "157 cards in your feed", "subtitles": [{"name": "Ad from logicmonitor.com"}, {"name": "Ad from pluralsight.com"}, {"name": "155 other cards"}], "time": "2021-02-08T04:23:41.430Z", "products": ["Discover"]}, {"header": "Discover", "title": "90 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from utexas.edu"}, {"name": "Ad from yieldstreet.com"}, {"name": "87 other cards"}], "time": "2021-02-07T04:56:29.059Z", "products": ["Discover"]}, {"header": "Discover", "title": "50 cards in your feed", "subtitles": [{"name": "Ad from gusto.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "Weather"}, {"name": "47 other cards"}], "time": "2021-02-06T04:15:45.893Z", "products": ["Discover"]}, {"header": "Discover", "title": "29 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from group-health-quotes.com"}, {"name": "Ad from universalfunding.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "24 other cards"}], "time": "2021-02-04T15:45:01.697Z", "products": ["Discover"]}, {"header": "Discover", "title": "25 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "22 other cards"}], "time": "2021-02-04T02:36:41.842Z", "products": ["Discover"]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from superside.com"}, {"name": "Weather"}, {"name": "9 other cards"}], "time": "2021-02-02T11:43:55.722Z", "products": ["Discover"]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Weather"}, {"name": "9 other cards"}], "time": "2021-02-02T03:13:01.164Z", "products": ["Discover"]}, {"header": "Discover", "title": "39 cards in your feed", "subtitles": [{"name": "Ad from delltechnologies.com"}, {"name": "Ad from fireeye.com"}, {"name": "Ad from medallia.com"}, {"name": "Discover more topics"}, {"name": "What To Stream - Card Summary"}, {"name": "What To Stream - Show More"}, {"name": "33 other cards"}], "time": "2021-01-31T23:29:24.687Z", "products": ["Discover"]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from leaffilter.com"}, {"name": "Discover more topics"}, {"name": "9 other cards"}], "time": "2021-01-29T21:07:47.628Z", "products": ["Discover"]}, {"header": "Discover", "title": "27 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "26 other cards"}], "time": "2021-01-29T00:05:12.815Z", "products": ["Discover"]}, {"header": "Discover", "title": "17 cards in your feed", "subtitles": [{"name": "Ad from gusto.com"}, {"name": "Discover more topics"}, {"name": "15 other cards"}], "time": "2021-01-28T00:45:43.340Z", "products": ["Discover"]}, {"header": "Discover", "title": "52 cards in your feed", "subtitles": [{"name": "Ad from leaffilter.com"}, {"name": "Ad from rainfactory.com"}, {"name": "Ad from se.com"}, {"name": "Weather"}, {"name": "48 other cards"}], "time": "2021-01-27T04:03:16.083Z", "products": ["Discover"]}, {"header": "Discover", "title": "62 cards in your feed", "subtitles": [{"name": "Ad from autonomous.ai"}, {"name": "Ad from intuit.com"}, {"name": "Ad from kount.com"}, {"name": "Ad from radpowerbikes.com"}, {"name": "Ad from rhone.com"}, {"name": "Weather"}, {"name": "56 other cards"}], "time": "2021-01-26T00:38:51.403Z", "products": ["Discover"]}, {"header": "Discover", "title": "74 cards in your feed", "subtitles": [{"name": "Ad from autonomous.ai"}, {"name": "Ad from bloomberg.com"}, {"name": "Ad from braincorp.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "Weather"}, {"name": "What To Stream - Card Summary"}, {"name": "What To Stream - Show More"}, {"name": "66 other cards"}], "time": "2021-01-25T04:22:02.703Z", "products": ["Discover"]}, {"header": "Discover", "title": "45 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Ad from pype.io"}, {"name": "Ad from splunk.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "Weather"}, {"name": "40 other cards"}], "time": "2021-01-24T04:34:21.613Z", "products": ["Discover"]}, {"header": "Discover", "title": "21 cards in your feed", "subtitles": [{"name": "Ad from intuit.com"}, {"name": "Ad from shinebathroom.com"}, {"name": "Weather"}, {"name": "18 other cards"}], "time": "2021-01-23T00:37:59.254Z", "products": ["Discover"]}, {"header": "Discover", "title": "42 cards in your feed", "subtitles": [{"name": "Ad from gusto.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from splunk.com"}, {"name": "Ad from tableau.com"}, {"name": "Weather"}, {"name": "37 other cards"}], "time": "2021-01-22T04:29:07.343Z", "products": ["Discover"]}, {"header": "Discover", "title": "20 cards in your feed", "subtitles": [{"name": "Ad from autonomous.ai"}, {"name": "Ad from mailchimp.com"}, {"name": "Weather"}, {"name": "17 other cards"}], "time": "2021-01-20T16:50:16.138Z", "products": ["Discover"]}, {"header": "Discover", "title": "52 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from gusto.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from kount.com"}, {"name": "Ad from simplisafe.com"}, {"name": "Weather"}, {"name": "46 other cards"}], "time": "2021-01-20T01:43:06.921Z", "products": ["Discover"]}, {"header": "Discover", "title": "28 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "Weather"}, {"name": "25 other cards"}], "time": "2021-01-18T21:41:20.704Z", "products": ["Discover"]}, {"header": "Discover", "title": "80 cards in your feed", "subtitles": [{"name": "Ad from braincorp.com"}, {"name": "Ad from homechef.com"}, {"name": "Ad from preply.com"}, {"name": "Ad from ubuntu.com"}, {"name": "Weather"}, {"name": "75 other cards"}], "time": "2021-01-18T03:10:36.454Z", "products": ["Discover"]}, {"header": "Discover", "title": "62 cards in your feed", "subtitles": [{"name": "Ad from auvik.com"}, {"name": "Ad from braincorp.com"}, {"name": "Ad from kount.com"}, {"name": "Ad from villanova.edu"}, {"name": "Ad from wildalaskancompany.com"}, {"name": "Ad from wsj.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "54 other cards"}], "time": "2021-01-17T04:25:28.317Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "<PERSON>"}], "time": "2021-04-03T17:37:52.220Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "11° warmer than today · See the full forecast - dismissed"}], "time": "2021-04-03T00:30:55.044Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS +2.39%<font color='#1e8e3e'>▲</font> TSLA -0.93%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "6° warmer than today · See the full forecast - dismissed"}], "time": "2021-04-02T01:16:25.452Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +5.08%<font color='#1e8e3e'>▲</font> ZS +3.55%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "18° cooler than today · See the full forecast - dismissed"}], "time": "2021-04-01T02:39:34.981Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +3.98%<font color='#1e8e3e'>▲</font> ZS +0.02%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "67° / 35° · Thunderstorm · See the full forecast - dismissed"}], "time": "2021-03-31T00:14:34.323Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS -2.37%<font color='#ea4335'>▼</font> TSLA -1.20%<font color='#ea4335'>▼</font> · Tap for more details"}, {"name": "6° warmer than today · See the full forecast"}], "time": "2021-03-30T03:23:57.836Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "16° cooler than today · See the full forecast - dismissed"}], "time": "2021-03-29T00:56:43.506Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "4° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-28T03:40:09.708Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -3.39%<font color='#ea4335'>▼</font> ZS -1.78%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "75° / 62° · Scattered thunderstorms · See the full forecast - dismissed"}], "time": "2021-03-27T03:02:46.474Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +1.61%<font color='#1e8e3e'>▲</font> ZS -1.28%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "7° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-26T00:54:21.424Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -4.82%<font color='#ea4335'>▼</font> ZS -4.32%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "12° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-25T03:15:26.890Z", "products": ["Discover"]}, {"header": "Discover", "title": "56 cards in your feed", "time": "2021-03-24T03:01:00.176Z", "products": ["Discover"]}, {"header": "Discover", "title": "3 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "See travel advisory and plans on google.com/travel - dismissed"}, {"name": "See ideas and more on google.com/travel - dismissed"}, {"name": "6° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-24T02:13:01.052Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +2.31%<font color='#1e8e3e'>▲</font> ZS +0.58%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "7° cooler than today · See the full forecast - dismissed"}], "time": "2021-03-23T00:51:13.273Z", "products": ["Discover"]}, {"header": "Discover", "title": "5 cards in your feed", "time": "2021-03-22T14:47:55.161Z", "products": ["Discover"]}, {"header": "Discover", "title": "62 cards in your feed", "time": "2021-03-22T03:22:11.982Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "62° / 47° · Partly cloudy · See the full forecast - dismissed"}], "time": "2021-03-22T00:57:15.308Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "9° warmer than today · See the full forecast"}], "time": "2021-03-21T03:29:10.559Z", "products": ["Discover"]}, {"header": "Discover", "title": "79 cards in your feed", "time": "2021-03-21T01:52:32.068Z", "products": ["Discover"]}, {"header": "Discover", "title": "25 cards in your feed", "time": "2021-03-20T03:54:26.585Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS +1.88%<font color='#1e8e3e'>▲</font> TSLA +0.26%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "6° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-20T01:42:43.415Z", "products": ["Discover"]}, {"header": "Discover", "title": "58 cards in your feed", "time": "2021-03-18T07:27:31.505Z", "products": ["Discover"]}, {"header": "Discover", "title": "176 cards in your feed", "time": "2021-03-18T03:57:55.080Z", "products": ["Discover"]}, {"header": "Discover", "title": "216 cards in your feed", "time": "2021-03-17T03:59:05.592Z", "products": ["Discover"]}, {"header": "Discover", "title": "44 cards in your feed", "time": "2021-03-15T06:39:53.660Z", "products": ["Discover"]}, {"header": "Discover", "title": "38 cards in your feed", "time": "2021-03-15T03:58:35.866Z", "products": ["Discover"]}, {"header": "Discover", "title": "38 cards in your feed", "subtitles": [{"name": "Ad from logicmonitor.com"}, {"name": "Ad from primecabinetry.com"}, {"name": "Ad from tableau.com"}, {"name": "Weather"}, {"name": "34 other cards"}], "time": "2021-01-16T00:19:05.272Z", "products": ["Discover"]}, {"header": "Discover", "title": "24 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "Weather"}, {"name": "20 other cards"}], "time": "2021-01-14T19:38:14.804Z", "products": ["Discover"]}, {"header": "Discover", "title": "30 cards in your feed", "subtitles": [{"name": "Ad from splunk.com"}, {"name": "Ad from tableau.com"}, {"name": "Weather"}, {"name": "27 other cards"}], "time": "2021-01-14T01:04:27.408Z", "products": ["Discover"]}, {"header": "Discover", "title": "30 cards in your feed", "subtitles": [{"name": "Ad from leaffilter.com"}, {"name": "Ad from tirerack.com"}, {"name": "Ad from zenefits.com"}, {"name": "Weather"}, {"name": "26 other cards"}], "time": "2021-01-13T04:30:23.271Z", "products": ["Discover"]}, {"header": "Discover", "title": "44 cards in your feed", "subtitles": [{"name": "Ad from kount.com"}, {"name": "Ad from mailchimp.com"}, {"name": "Ad from tableau.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "39 other cards"}], "time": "2021-01-12T02:37:30.938Z", "products": ["Discover"]}, {"header": "Discover", "title": "52 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from homechef.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from relaygo.com"}, {"name": "Ad from wsu.edu"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "45 other cards"}], "time": "2021-01-11T03:34:41.764Z", "products": ["Discover"]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "10 other cards"}], "time": "2021-01-10T03:17:51.028Z", "products": ["Discover"]}, {"header": "Discover", "title": "44 cards in your feed", "subtitles": [{"name": "Ad from kount.com"}, {"name": "Ad from zenefits.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "40 other cards"}], "time": "2021-01-09T01:37:09.726Z", "products": ["Discover"]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from kachava.com"}, {"name": "Weather"}, {"name": "9 other cards"}], "time": "2021-01-07T15:44:05.068Z", "products": ["Discover"]}, {"header": "Discover", "title": "41 cards in your feed", "subtitles": [{"name": "Ad from clickup.com"}, {"name": "Ad from homechef.com"}, {"name": "Ad from utexas.edu"}, {"name": "Ad from zenefits.com"}, {"name": "Weather"}, {"name": "36 other cards"}], "time": "2021-01-07T04:44:51.923Z", "products": ["Discover"]}, {"header": "Discover", "title": "20 cards in your feed", "subtitles": [{"name": "Ad from clickup.com"}, {"name": "Weather"}, {"name": "18 other cards"}], "time": "2021-01-05T23:30:26.020Z", "products": ["Discover"]}, {"header": "Discover", "title": "77 cards in your feed", "subtitles": [{"name": "Ad from autonomous.ai"}, {"name": "Ad from google.com"}, {"name": "Ad from homechef.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from tableau.com"}, {"name": "Ad from wildalaskancompany.com"}, {"name": "Weather"}, {"name": "70 other cards"}], "time": "2021-01-05T04:43:33.219Z", "products": ["Discover"]}, {"header": "Discover", "title": "45 cards in your feed", "subtitles": [{"name": "Ad from batteriesplusfranchise.com"}, {"name": "Ad from google.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from sidecarhealthinsurance.com"}, {"name": "Ad from thepowermba.com"}, {"name": "Ad from tirerack.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "37 other cards"}], "time": "2021-01-04T04:46:20.797Z", "products": ["Discover"]}, {"header": "Discover", "title": "54 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from homechef.com"}, {"name": "Ad from kount.com"}, {"name": "Ad from lightform.com"}, {"name": "Ad from viome.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "Weather"}, {"name": "47 other cards"}], "time": "2021-01-03T04:56:02.321Z", "products": ["Discover"]}, {"header": "Discover", "title": "11 cards in your feed", "subtitles": [{"name": "Ad from sidecarhealthinsurance.com"}, {"name": "Weather"}, {"name": "9 other cards"}], "time": "2021-01-01T14:51:56.364Z", "products": ["Discover"]}, {"header": "Discover", "title": "36 cards in your feed", "subtitles": [{"name": "Ad from barrons.com"}, {"name": "Ad from weareplannedparenthoodaction.org"}, {"name": "Ad from yieldstreet.com"}, {"name": "Weather"}, {"name": "32 other cards"}], "time": "2020-12-31T20:43:42.459Z", "products": ["Discover"]}, {"header": "Discover", "title": "69 cards in your feed", "subtitles": [{"name": "Ad from barrons.com"}, {"name": "Ad from google.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Ad from unicefusa.org"}, {"name": "Ad from viome.com"}, {"name": "Ad from wildalaskancompany.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "What To Stream - Card Summary"}, {"name": "What To Stream - Show More"}, {"name": "58 other cards"}], "time": "2020-12-31T04:48:45.899Z", "products": ["Discover"]}, {"header": "Discover", "title": "40 cards in your feed", "subtitles": [{"name": "Ad from cloudian.com"}, {"name": "Ad from google.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Ad from weareplannedparenthoodaction.org"}, {"name": "Weather"}, {"name": "35 other cards"}], "time": "2020-12-30T03:57:22.507Z", "products": ["Discover"]}, {"header": "Discover", "title": "31 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Weather"}, {"name": "29 other cards"}], "time": "2020-12-29T01:19:51.227Z", "products": ["Discover"]}, {"header": "Discover", "title": "35 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Ad from sidecarhealthinsurance.com"}, {"name": "Ad from trimble.com"}, {"name": "Ad from unbounce.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "29 other cards"}], "time": "2020-12-28T03:55:56.529Z", "products": ["Discover"]}, {"header": "Discover", "title": "64 cards in your feed", "subtitles": [{"name": "Ad from accent-systems.com"}, {"name": "Ad from google.com"}, {"name": "Ad from kickstarter.com"}, {"name": "Ad from seooptimizers.com"}, {"name": "Ad from utexas.edu"}, {"name": "Ad from wildalaskancompany.com"}, {"name": "Weather"}, {"name": "57 other cards"}], "time": "2020-12-27T04:16:41.172Z", "products": ["Discover"]}, {"header": "Discover", "title": "118 cards in your feed", "subtitles": [{"name": "Ad from campingworld.com"}, {"name": "Ad from google.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Ad from purdue.edu"}, {"name": "Ad from wired.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "110 other cards"}], "time": "2020-12-26T04:13:47.867Z", "products": ["Discover"]}, {"header": "Discover", "title": "64 cards in your feed", "subtitles": [{"name": "Ad from acefitness.org"}, {"name": "Ad from google.com"}, {"name": "Ad from oralb.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "59 other cards"}], "time": "2020-12-25T04:24:37.095Z", "products": ["Discover"]}, {"header": "Discover", "title": "Asked for more like \"Charting a bullish backdrop as the S&P 500's wild 2020 ride ...\" in Discover", "time": "2020-12-24T06:52:37.210Z", "products": ["Discover"]}, {"header": "Discover", "title": "90 cards in your feed", "subtitles": [{"name": "Ad from bloomberg.com"}, {"name": "Ad from fireeye.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from oralb.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "Weather"}, {"name": "What To Stream - Card Summary"}, {"name": "What To Stream - Show More"}, {"name": "82 other cards"}], "time": "2020-12-24T04:23:12.049Z", "products": ["Discover"]}, {"header": "Discover", "title": "31 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from roofle.com"}, {"name": "Weather"}, {"name": "27 other cards"}], "time": "2020-12-23T00:40:20.821Z", "products": ["Discover"]}, {"header": "Discover", "title": "32 cards in your feed", "subtitles": [{"name": "Ad from hellotushy.com"}, {"name": "Ad from splunk.com"}, {"name": "Ad from unicefusa.org"}, {"name": "Weather"}, {"name": "28 other cards"}], "time": "2020-12-22T01:09:35.777Z", "products": ["Discover"]}, {"header": "Discover", "title": "51 cards in your feed", "subtitles": [{"name": "Ad from hellotushy.com"}, {"name": "Ad from roofle.com"}, {"name": "Ad from sidecarhealthinsurance.com"}, {"name": "Ad from unbounce.com"}, {"name": "Ad from vimeo.com"}, {"name": "Weather"}, {"name": "45 other cards"}], "time": "2020-12-21T03:24:05.696Z", "products": ["Discover"]}, {"header": "Discover", "title": "50 cards in your feed", "subtitles": [{"name": "Ad from allego.com"}, {"name": "Ad from clicksend.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from sidecarhealthinsurance.com"}, {"name": "Ad from ting.com"}, {"name": "Weather"}, {"name": "44 other cards"}], "time": "2020-12-20T01:12:44.737Z", "products": ["Discover"]}, {"header": "Discover", "title": "74 cards in your feed", "subtitles": [{"name": "Ad from google.com"}, {"name": "Ad from intuit.com"}, {"name": "Ad from utexas.edu"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "69 other cards"}], "time": "2020-12-19T04:16:56.938Z", "products": ["Discover"]}, {"header": "Discover", "title": "46 cards in your feed", "subtitles": [{"name": "Ad from endurancelights.com"}, {"name": "Ad from intuit.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from youtube.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "40 other cards"}], "time": "2020-12-18T02:45:42.955Z", "products": ["Discover"]}, {"header": "Discover", "title": "43 cards in your feed", "subtitles": [{"name": "Ad from aerishealth.com"}, {"name": "Ad from mit.edu"}, {"name": "Ad from stasherbag.com"}, {"name": "Ad from youtube.com"}, {"name": "Weather"}, {"name": "38 other cards"}], "time": "2020-12-16T13:34:28.908Z", "products": ["Discover"]}, {"header": "Discover", "title": "61 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from newchapter.com"}, {"name": "Ad from stripesagency.com"}, {"name": "Weather"}, {"name": "55 other cards"}], "time": "2020-12-16T00:31:58.887Z", "products": ["Discover"]}, {"header": "Discover", "title": "53 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from hellotushy.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "Weather"}, {"name": "What To Stream - Card Summary"}, {"name": "What To Stream - Show More"}, {"name": "46 other cards"}], "time": "2020-12-15T01:34:58.361Z", "products": ["Discover"]}, {"header": "Discover", "title": "67 cards in your feed", "subtitles": [{"name": "Ad from hellotushy.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "Ad from mode.com"}, {"name": "Ad from splunk.com"}, {"name": "Ad from utexas.edu"}, {"name": "Ad from yieldstreet.com"}, {"name": "Weather"}, {"name": "What To Stream - Card Summary"}, {"name": "What To Stream - Show More"}, {"name": "57 other cards"}], "time": "2020-12-14T04:16:03.937Z", "products": ["Discover"]}, {"header": "Discover", "title": "61 cards in your feed", "subtitles": [{"name": "Ad from pluralsight.com"}, {"name": "Ad from rosettastone.com"}, {"name": "Ad from seooptimizers.com"}, {"name": "Weather"}, {"name": "What To Stream - Card Summary"}, {"name": "What To Stream - Show More"}, {"name": "55 other cards"}], "time": "2020-12-13T04:29:31.328Z", "products": ["Discover"]}, {"header": "Discover", "title": "35 cards in your feed", "subtitles": [{"name": "Ad from ecornell.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from mirror.co"}, {"name": "Ad from sidecarhealthinsurance.com"}, {"name": "Weather"}, {"name": "30 other cards"}], "time": "2020-12-12T03:50:02.440Z", "products": ["Discover"]}, {"header": "Discover", "title": "18 cards in your feed", "subtitles": [{"name": "Ad from ecornell.com"}, {"name": "17 other cards"}], "time": "2020-12-11T01:28:44.655Z", "products": ["Discover"]}, {"header": "Discover", "title": "28 cards in your feed", "subtitles": [{"name": "Ad from ecornell.com"}, {"name": "Ad from mirror.co"}, {"name": "Weather"}, {"name": "25 other cards"}], "time": "2020-12-09T21:37:13.627Z", "products": ["Discover"]}, {"header": "Discover", "title": "24 cards in your feed", "subtitles": [{"name": "Ad from ecornell.com"}, {"name": "Ad from lincoln.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "20 other cards"}], "time": "2020-12-09T03:03:40.840Z", "products": ["Discover"]}, {"header": "Discover", "title": "42 cards in your feed", "subtitles": [{"name": "Ad from ecornell.com"}, {"name": "Ad from tableau.com"}, {"name": "Ad from the-financialnews.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "36 other cards"}], "time": "2020-12-08T03:13:07.508Z", "products": ["Discover"]}, {"header": "Discover", "title": "30 cards in your feed", "subtitles": [{"name": "Ad from wpengine.com"}, {"name": "Ad from zeiss.com"}, {"name": "Weather"}, {"name": "27 other cards"}], "time": "2020-12-06T19:00:12.073Z", "products": ["Discover"]}, {"header": "Discover", "title": "40 cards in your feed", "subtitles": [{"name": "Ad from logicmonitor.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from splunk.com"}, {"name": "Ad from yieldstreet.com"}, {"name": "Weather"}, {"name": "35 other cards"}], "time": "2020-12-06T03:32:55.283Z", "products": ["Discover"]}, {"header": "Discover", "title": "43 cards in your feed", "subtitles": [{"name": "Ad from mit.edu"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from splunk.com"}, {"name": "Ad from tableau.com"}, {"name": "Weather"}, {"name": "38 other cards"}], "time": "2020-12-05T04:21:02.482Z", "products": ["Discover"]}, {"header": "Discover", "title": "Asked for more like a topic in Discover", "time": "2020-12-04T05:25:36.716Z", "products": ["Discover"]}, {"header": "Discover", "title": "52 cards in your feed", "subtitles": [{"name": "Ad from establishedtitles.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "49 other cards"}], "time": "2020-12-04T03:44:40.461Z", "products": ["Discover"]}, {"header": "Discover", "title": "43 cards in your feed", "subtitles": [{"name": "Ad from leaffilter.com"}, {"name": "Ad from mudwtr.com"}, {"name": "Ad from radio.com"}, {"name": "Weather"}, {"name": "39 other cards"}], "time": "2020-12-02T22:18:29.275Z", "products": ["Discover"]}, {"header": "Discover", "title": "50 cards in your feed", "subtitles": [{"name": "Ad from investmentfundsecrets.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from logicmonitor.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "45 other cards"}], "time": "2020-12-02T03:58:09.472Z", "products": ["Discover"]}, {"header": "Discover", "title": "32 cards in your feed", "subtitles": [{"name": "Ad from samsung.com"}, {"name": "Ad from simplilearn.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "28 other cards"}], "time": "2020-11-30T21:46:34.796Z", "products": ["Discover"]}, {"header": "Discover", "title": "29 cards in your feed", "subtitles": [{"name": "Ad from hedgeye.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "25 other cards"}], "time": "2020-11-30T03:21:48.826Z", "products": ["Discover"]}, {"header": "Discover", "title": "58 cards in your feed", "subtitles": [{"name": "Ad from splunk.com"}, {"name": "Ad from tableau.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "54 other cards"}], "time": "2020-11-29T04:54:37.444Z", "products": ["Discover"]}, {"header": "Discover", "title": "22 cards in your feed", "subtitles": [{"name": "Ad from pluralsight.com"}, {"name": "Ad from splunk.com"}, {"name": "Weather"}, {"name": "19 other cards"}], "time": "2020-11-27T18:58:52.568Z", "products": ["Discover"]}, {"header": "Discover", "title": "48 cards in your feed", "subtitles": [{"name": "Ad from splunk.com"}, {"name": "Ad from umd.edu"}, {"name": "Weather"}, {"name": "45 other cards"}], "time": "2020-11-27T03:38:15.003Z", "products": ["Discover"]}, {"header": "Discover", "title": "45 cards in your feed", "subtitles": [{"name": "Ad from bdbiosciences.com"}, {"name": "Ad from bloomsybox.com"}, {"name": "Ad from fireeye.com"}, {"name": "Ad from goto.com"}, {"name": "Ad from splunk.com"}, {"name": "Discover more topics"}, {"name": "Weather"}, {"name": "38 other cards"}], "time": "2020-11-26T04:35:56.229Z", "products": ["Discover"]}, {"header": "Discover", "title": "36 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from zenefits.com"}, {"name": "Weather"}, {"name": "33 other cards"}], "time": "2020-11-25T03:42:46.719Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}, {"name": "1 other notification"}], "time": "2020-11-24T04:39:11.096Z", "products": ["Discover"]}, {"header": "Discover", "title": "33 cards in your feed", "subtitles": [{"name": "Ad from bloomsybox.com"}, {"name": "Ad from xendoo.com"}, {"name": "Ad from zenefits.com"}, {"name": "Weather"}, {"name": "29 other cards"}], "time": "2020-11-23T23:22:02.660Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-23T04:04:34.989Z", "products": ["Discover"]}, {"header": "Discover", "title": "54 cards in your feed", "subtitles": [{"name": "Ad from ember.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from mode.com"}, {"name": "Ad from utexas.edu"}, {"name": "Ad from xendoo.com"}, {"name": "Ad from youtube.com"}, {"name": "Weather"}, {"name": "47 other cards"}], "time": "2020-11-22T23:40:00.380Z", "products": ["Discover"]}, {"header": "Discover", "title": "48 cards in your feed", "subtitles": [{"name": "Ad from kitchencabinetkings.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Ad from netcapital.com"}, {"name": "Weather"}, {"name": "44 other cards"}], "time": "2020-11-22T01:37:45.221Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ8zhA2s5XQIgR9izeGnbAqiw"}], "time": "2020-11-22T01:37:45.221Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ8zhA2s5XQIgR9izeGnbAqiw"}, {"name": "1 other notification"}], "time": "2020-11-21T02:58:55.978Z", "products": ["Discover"]}, {"header": "Discover", "title": "50 cards in your feed", "subtitles": [{"name": "Ad from barrons.com"}, {"name": "Ad from google.com"}, {"name": "Ad from plex.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "45 other cards"}], "time": "2020-11-21T02:58:55.978Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ8zhA2s5XQIgR9izeGnbAqiw"}, {"name": "1 other notification"}], "time": "2020-11-20T04:32:13.058Z", "products": ["Discover"]}, {"header": "Discover", "title": "44 cards in your feed", "subtitles": [{"name": "Ad from kachava.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from utexas.edu"}, {"name": "Weather"}, {"name": "40 other cards"}], "time": "2020-11-19T23:46:28.735Z", "products": ["Discover"]}, {"header": "Discover", "title": "20 cards in your feed", "subtitles": [{"name": "Ad from splunk.com"}, {"name": "Discover more topics"}, {"name": "18 other cards"}], "time": "2020-11-19T03:00:28.868Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJc7PZ4ByYSIgR2wd9VyVIgSc"}], "time": "2020-11-19T01:37:55.532Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-18T04:26:01.262Z", "products": ["Discover"]}, {"header": "Discover", "title": "28 cards in your feed", "subtitles": [{"name": "Ad from logicmonitor.com"}, {"name": "Ad from splunk.com"}, {"name": "Weather"}, {"name": "25 other cards"}], "time": "2020-11-17T15:50:26.415Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}, {"name": "1 other notification"}], "time": "2020-11-17T02:37:36.552Z", "products": ["Discover"]}, {"header": "Discover", "title": "61 cards in your feed", "subtitles": [{"name": "Ad from discounttwo-wayradio.com"}, {"name": "Ad from kachava.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Weather"}, {"name": "56 other cards"}], "time": "2020-11-17T02:37:36.552Z", "products": ["Discover"]}, {"header": "Discover", "title": "33 cards in your feed", "subtitles": [{"name": "Ad from fireeye.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Weather"}, {"name": "30 other cards"}], "time": "2020-11-16T03:50:21.212Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-16T00:50:11.714Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-15T00:42:02.455Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS +1.88%<font color='#1e8e3e'>▲</font> TSLA +0.26%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "Feels like 44° · Partly cloudy - dismissed"}], "time": "2021-03-19T22:15:47.130Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -6.93%<font color='#ea4335'>▼</font> ZS -3.84%<font color='#ea4335'>▼</font> · Tap for more details"}, {"name": "22° cooler than today · See the full forecast"}], "time": "2021-03-19T03:43:21.375Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +3.68%<font color='#1e8e3e'>▲</font> ZS +1.73%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "10° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-18T03:43:40.688Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -4.39%<font color='#ea4335'>▼</font> ZS -1.13%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "17° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-17T01:29:22.952Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +2.05%<font color='#1e8e3e'>▲</font> ZS -0.18%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "6° cooler than today · See the full forecast - dismissed"}], "time": "2021-03-16T01:06:46.156Z", "products": ["Discover"]}, {"header": "Discover", "title": "21 cards in your feed", "time": "2021-03-15T00:55:57.907Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "18° cooler than today · See the full forecast - dismissed"}], "time": "2021-03-15T00:48:49.158Z", "products": ["Discover"]}, {"header": "Discover", "title": "94 cards in your feed", "time": "2021-03-14T01:44:35.395Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "69° / 40° · Partly cloudy · See the full forecast - dismissed"}], "time": "2021-03-14T01:24:54.087Z", "products": ["Discover"]}, {"header": "Discover", "title": "156 cards in your feed", "time": "2021-03-13T04:01:35.043Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS -2.26%<font color='#ea4335'>▼</font> TSLA -0.84%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "13° cooler than today · See the full forecast - dismissed"}], "time": "2021-03-13T02:15:19.965Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "ZS +4.89%<font color='#1e8e3e'>▲</font> TSLA +4.72%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "77° / 51° · Partly cloudy · See the full forecast - dismissed"}], "time": "2021-03-12T04:52:25.794Z", "products": ["Discover"]}, {"header": "Discover", "title": "26 cards in your feed", "time": "2021-03-12T02:51:09.624Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -0.82%<font color='#ea4335'>▼</font> ZS -0.75%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "75° / 53° · Partly cloudy · See the full forecast"}], "time": "2021-03-11T04:51:16.902Z", "products": ["Discover"]}, {"header": "Discover", "title": "6 cards in your feed", "time": "2021-03-11T02:11:26.729Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA +19.64%<font color='#1e8e3e'>▲</font> ZS +8.10%<font color='#1e8e3e'>▲</font> · Tap for more details - dismissed"}, {"name": "73° / 45° · Mostly sunny · See the full forecast - dismissed"}], "time": "2021-03-10T04:02:59.864Z", "products": ["Discover"]}, {"header": "Discover", "title": "130 cards in your feed", "time": "2021-03-10T01:20:02.714Z", "products": ["Discover"]}, {"header": "Discover", "title": "59 cards in your feed", "time": "2021-03-09T04:31:54.997Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "TSLA -5.84%<font color='#ea4335'>▼</font> ZS -4.29%<font color='#ea4335'>▼</font> · Tap for more details - dismissed"}, {"name": "10° warmer than today · See the full forecast - dismissed"}], "time": "2021-03-09T03:20:28.461Z", "products": ["Discover"]}, {"header": "Discover", "title": "22 cards in your feed", "time": "2021-03-08T03:00:44.099Z", "products": ["Discover"]}, {"header": "Discover", "title": "13 cards in your feed", "time": "2021-03-06T03:45:02.433Z", "products": ["Discover"]}, {"header": "Discover", "title": "132 cards in your feed", "time": "2021-03-05T04:34:15.400Z", "products": ["Discover"]}, {"header": "Discover", "title": "60 cards in your feed", "time": "2021-03-04T02:40:06.476Z", "products": ["Discover"]}, {"header": "Discover", "title": "96 cards in your feed", "time": "2021-03-03T04:48:55.345Z", "products": ["Discover"]}, {"header": "Discover", "title": "53 cards in your feed", "time": "2021-03-02T04:41:47.445Z", "products": ["Discover"]}, {"header": "Discover", "title": "31 cards in your feed", "time": "2021-03-01T03:14:34.475Z", "products": ["Discover"]}, {"header": "Discover", "title": "28 cards in your feed", "subtitles": [{"name": "Ad from hydrow.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Amazon Web Services"}, {"name": "Belief"}, {"name": "Clothing"}, {"name": "Coronavirus disease 2019"}, {"name": "Discover more topics"}, {"name": "Enterprise"}, {"name": "Entertainment"}, {"name": "Food"}, {"name": "Investment"}, {"name": "<PERSON>"}, {"name": "Lie"}, {"name": "Money"}, {"name": "Pfizer"}, {"name": "Psychology"}, {"name": "Renewable energy"}, {"name": "Science"}, {"name": "Skin care"}, {"name": "Solar power"}, {"name": "Staycation"}, {"name": "Stock"}, {"name": "Technology"}, {"name": "Tesla Model S"}, {"name": "Tesla Supercharger"}, {"name": "Travel"}, {"name": "Weather"}], "time": "2020-11-14T21:02:35.948Z", "products": ["Discover"]}, {"header": "Discover", "title": "25 cards in your feed", "subtitles": [{"name": "Ad from leaffilter.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Beauty"}, {"name": "BioNTech SE"}, {"name": "Cattle"}, {"name": "<PERSON>"}, {"name": "Coronavirus disease 2019"}, {"name": "Enterprise"}, {"name": "Entertainment"}, {"name": "Exercise"}, {"name": "Food"}, {"name": "Google Play Music"}, {"name": "<PERSON>"}, {"name": "Health"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Money"}, {"name": "NASDAQ:TSLA"}, {"name": "Renewable energy"}, {"name": "San Francisco Chronicle"}, {"name": "Science"}, {"name": "Stock"}, {"name": "Technology"}, {"name": "Tesla, Inc."}, {"name": "Weather"}], "time": "2020-11-14T03:23:05.827Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "NASDAQ:ZS"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-14T02:17:20.350Z", "products": ["Discover"]}, {"header": "Discover", "title": "3 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "Flash Flood Warning at North Carolina", "url": "https://maps.google.com/?q=35.622750,-79.201562"}, {"name": "NASDAQ:ZS"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-13T01:25:53.994Z", "products": ["Discover"]}, {"header": "Discover", "title": "31 cards in your feed", "subtitles": [{"name": "Ad from kount.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Ad from unbounce.com"}, {"name": "Ad from verizonconnect.com"}, {"name": "Car"}, {"name": "Cattle"}, {"name": "Cooking"}, {"name": "Coronavirus disease 2019"}, {"name": "Electric car"}, {"name": "Enterprise"}, {"name": "Entertainment"}, {"name": "Food"}, {"name": "Google Ads"}, {"name": "Google Play Music"}, {"name": "Health"}, {"name": "<PERSON>"}, {"name": "Pfizer"}, {"name": "Politics"}, {"name": "Psychology"}, {"name": "Saturday Night Live"}, {"name": "Science"}, {"name": "Skin care"}, {"name": "Solar power"}, {"name": "Sports"}, {"name": "Stock"}, {"name": "Technology"}, {"name": "Tesla Model Y"}, {"name": "<PERSON><PERSON>"}, {"name": "Wearable technology"}, {"name": "Weather"}, {"name": "Zoom Video Communications"}], "time": "2020-11-13T01:25:53.994Z", "products": ["Discover"]}, {"header": "Discover", "title": "25 cards in your feed", "subtitles": [{"name": "Ad from kount.com"}, {"name": "Ad from pluralsight.com"}, {"name": "Airplane"}, {"name": "Car"}, {"name": "Cattle"}, {"name": "Cooking"}, {"name": "Entertainment"}, {"name": "Food"}, {"name": "Google Ads"}, {"name": "Health"}, {"name": "Interpersonal relationship"}, {"name": "<PERSON>"}, {"name": "Lifestyle"}, {"name": "<PERSON>"}, {"name": "NASDAQ:TSLA"}, {"name": "North Carolina"}, {"name": "Politics"}, {"name": "Saturday Night Live"}, {"name": "Science"}, {"name": "Stock"}, {"name": "Tax"}, {"name": "Technology"}, {"name": "Tesla Model 3"}, {"name": "Travel"}, {"name": "Weather"}], "time": "2020-11-12T04:11:40.375Z", "products": ["Discover"]}, {"header": "Discover", "title": "1 notification", "subtitles": [{"name": "Including topics:"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-12T00:13:05.580Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "NASDAQ:TSLA"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-11T02:11:59.953Z", "products": ["Discover"]}, {"header": "Discover", "title": "2 notifications", "subtitles": [{"name": "Including topics:"}, {"name": "NASDAQ:ZS"}, {"name": "Weather at a location", "url": "https://www.google.com/maps/place/?q=place_id:ChIJ9-BRny9arIkRrfARilK2kGc"}], "time": "2020-11-10T04:58:58.277Z", "products": ["Discover"]}, {"header": "Discover", "title": "28 cards in your feed", "subtitles": [{"name": "Ad from epson.com"}, {"name": "Ad from google.com"}, {"name": "Ad from kount.com"}, {"name": "Ad from leaffilter.com"}, {"name": "Ad from mintmobile.com"}, {"name": "Amateur astronomy"}, {"name": "Application programming interface"}, {"name": "Car"}, {"name": "Data"}, {"name": "Data science"}, {"name": "Department of motor vehicles"}, {"name": "Electric car"}, {"name": "Entertainment"}, {"name": "Finance"}, {"name": "Fundraising"}, {"name": "Google"}, {"name": "Internet of things"}, {"name": "Investment"}, {"name": "Marsup<PERSON><PERSON>"}, {"name": "Money"}, {"name": "Net metering"}, {"name": "Nonprofit organization"}, {"name": "Paycheck Protection Program"}, {"name": "Politics"}, {"name": "Technology"}, {"name": "Tesla, Inc."}, {"name": "Virgin Hyperloop"}, {"name": "Weather"}], "time": "2020-11-10T00:02:43.614Z", "products": ["Discover"]}]