[{"header": "YouTube", "title": "Watched Spongebob Squarepants - Write That Down", "titleUrl": "https://www.youtube.com/watch?v=_tZckjQylGU", "subtitles": [{"name": "AreaEightyNine", "url": "https://www.youtube.com/channel/UCW_SKBwR1TMaclnvDfkezwQ"}], "time": "2021-04-18T04:19:21.305Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched +++ Livestream of the ID.6 World Online Premiere +++", "titleUrl": "https://www.youtube.com/watch?v=Rf0y4FZVmcs", "subtitles": [{"name": "Volkswagen News", "url": "https://www.youtube.com/channel/UCJxMw5IralIBLLr0RYVrikw"}], "time": "2021-04-18T01:52:18.711Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched New Tesla Model S: Best Luxury EV Sedan vs EQS, Air & Taycan?", "titleUrl": "https://www.youtube.com/watch?v=qQnBG4jvLDY", "subtitles": [{"name": "Cleanerwatt", "url": "https://www.youtube.com/channel/UCyj5OG4nbnQMnueBOvryi9g"}], "time": "2021-04-17T11:21:00.491Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Fi Original Stories: Connie & Astro", "titleUrl": "https://www.youtube.com/watch?v=FUF4dhdwx1c", "subtitles": [{"name": "Fi", "url": "https://www.youtube.com/channel/UC_XGyYlUJt22HiRsA-Gdl-Q"}], "time": "2021-04-17T11:13:28.081Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched How to Pronounce Liza - PronounceNames.com", "titleUrl": "https://www.youtube.com/watch?v=hOPm8ONYfWY", "subtitles": [{"name": "Pronounce Names", "url": "https://www.youtube.com/channel/UCENv4zYsUl1dxeg346hVVLQ"}], "time": "2021-04-17T00:21:34.291Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Are you are a good liar? Find out in 5 seconds", "titleUrl": "https://www.youtube.com/watch?v=yRAmvLV_EmY", "subtitles": [{"name": "In59seconds", "url": "https://www.youtube.com/channel/UCoUXxtd712vGe5p5lBk_eMg"}], "time": "2021-04-16T02:18:02.383Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 2021 Volkswagen ID.4: E3 - Hoist Review, Front and Rear Suspension", "titleUrl": "https://www.youtube.com/watch?v=HkJXkWC9G_0", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCj--iMtToRO_cGG_fpmP5XQ"}], "time": "2021-04-15T16:27:28.757Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched TESLA MODEL Y TEST DRIVE (2021 Tesla Model Y 7 Seater)", "titleUrl": "https://www.youtube.com/watch?v=G-rMFDKFZm0", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCCGR4VO2jTrdx2nRwEA5v2w"}], "time": "2021-04-14T02:53:11.247Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Best Caddyshack Quotes - HD", "titleUrl": "https://www.youtube.com/watch?v=DTa94kSlV0U", "subtitles": [{"name": "Varuna", "url": "https://www.youtube.com/channel/UCEyiOPwdlsqBf7sGHX_OylQ"}], "time": "2021-04-12T00:09:13.095Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for bill murray na na na  na na", "titleUrl": "https://www.youtube.com/results?search_query=bill+murray+na+na+na++na+na", "time": "2021-04-12T00:09:09.322Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for bill murray na na na ", "titleUrl": "https://www.youtube.com/results?search_query=bill+murray+na+na+na+", "time": "2021-04-12T00:08:59.644Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Monkey MindPong", "titleUrl": "https://www.youtube.com/watch?v=rsCul1sp4hQ", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCLt4d8cACHzrVvAz9gtaARA"}], "time": "2021-04-11T00:04:12.736Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Creepy Line - Full Documentary on Social Media's manipulation of society", "titleUrl": "https://www.youtube.com/watch?v=0v6KBGr5IzY", "subtitles": [{"name": "Janson Media", "url": "https://www.youtube.com/channel/UCc_53-Ev85K8B86f8wbWwXA"}], "time": "2021-04-10T17:19:26.067Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Alaska 2020", "titleUrl": "https://www.youtube.com/watch?v=FcvUi6w6YHk", "subtitles": [{"name": "Aley Travels", "url": "https://www.youtube.com/channel/UCcslHRLNkOBaLdDdr33nLCw"}], "time": "2021-04-08T03:05:20.032Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Maine 2020", "titleUrl": "https://www.youtube.com/watch?v=WL1gNygkwl8", "subtitles": [{"name": "Aley Travels", "url": "https://www.youtube.com/channel/UCcslHRLNkOBaLdDdr33nLCw"}], "time": "2021-04-08T03:04:38.787Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Miami & The Keys 2021", "titleUrl": "https://www.youtube.com/watch?v=fHhZStE_ToA", "subtitles": [{"name": "Aley Travels", "url": "https://www.youtube.com/channel/UCcslHRLNkOBaLdDdr33nLCw"}], "time": "2021-04-08T03:03:52.738Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON> - Speaker <PERSON><PERSON> 1", "titleUrl": "https://www.youtube.com/watch?v=aB4sCwYE4-I", "subtitles": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCC19VQF8O_ZAcNaWowOK3Vw"}], "time": "2021-04-05T01:20:18.179Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Beauty and the Beast - Gaston", "titleUrl": "https://www.youtube.com/watch?v=VuJTqmpBnI0", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCgkx7uwfopuU-c4bT8bzzZg"}], "time": "2021-04-04T13:05:30.027Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched [FNAF SFM] Try Not To Laugh Challenge (Funny FNAF Animations)", "titleUrl": "https://www.youtube.com/watch?v=MQgZxEZM56A", "subtitles": [{"name": "Animation Time", "url": "https://www.youtube.com/channel/UCrSGNHINpnQsNCZngtEcJjg"}], "time": "2021-04-01T14:53:45.034Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Fnaf Sfm keep it a secret", "titleUrl": "https://www.youtube.com/watch?v=0k4j8sUC2qY", "subtitles": [{"name": "Some Birby Boi", "url": "https://www.youtube.com/channel/UC0RH4be8f0oHo5qWWR6Lb3A"}], "time": "2021-04-01T14:53:19.515Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Stranded cargo ship in Suez Canal could 'take weeks' to free | DW News", "titleUrl": "https://www.youtube.com/watch?v=Hq80pHnUMJg", "subtitles": [{"name": "DW News", "url": "https://www.youtube.com/channel/UCknLrEdhRCp1aegoMqRaCZg"}], "time": "2021-03-31T16:32:37.501Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Subscribed to Live Learn Innovate", "titleUrl": "https://www.youtube.com/channel/UCgl49JuiO-sO0-oL0Fswr2g", "time": "2021-03-31T16:31:43.437Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Our Why", "titleUrl": "https://www.youtube.com/watch?v=zQrufVEaGn0", "subtitles": [{"name": "Live Learn Innovate", "url": "https://www.youtube.com/channel/UCgl49JuiO-sO0-oL0Fswr2g"}], "time": "2021-03-31T16:30:59.709Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Secret - Official Video by <PERSON> Pierces", "titleUrl": "https://www.youtube.com/watch?v=HzNFwxsSPwU", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCfkUg750Sdm5pmjrayPjfLA"}], "time": "2021-03-31T13:43:16.417Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> on Muslims (C-SPAN)", "titleUrl": "https://www.youtube.com/watch?v=-sz0KY-3PbQ", "subtitles": [{"name": "C-SPAN", "url": "https://www.youtube.com/channel/UCb--64Gl51jIEVE-GLDAVTg"}], "time": "2021-03-28T11:30:25.567Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched President <PERSON>'s Muslim-Targeted Travel Ban: The Daily Show", "titleUrl": "https://www.youtube.com/watch?v=IxhGjDyBT30", "subtitles": [{"name": "The Daily Show with <PERSON>", "url": "https://www.youtube.com/channel/UCwWhs_6x42TyRM4Wstoq8HA"}], "time": "2021-03-28T11:28:02.383Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON>'s entire speech to Muslim world", "titleUrl": "https://www.youtube.com/watch?v=Udz9b5BThnw", "subtitles": [{"name": "CNN", "url": "https://www.youtube.com/channel/UCupvZG-5ko_eiXAupbDfxWw"}], "time": "2021-03-28T11:22:10.703Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Beginner React.js Coding Interview (ft. <PERSON><PERSON><PERSON>)", "titleUrl": "https://www.youtube.com/watch?v=gnkrDse9QKc", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-8QAzbLcRglXeN_MY9blyw"}], "time": "2021-03-27T18:44:37.698Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched FSDBeta 8.2 - 2021.4.11.1 - Expansion of Beta Group Thoughts for All New Beta Testers - Must Watch", "titleUrl": "https://www.youtube.com/watch?v=GE1hhg5MNDo", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCwdbsDtaMAh6QXvcbp08YzQ"}], "time": "2021-03-27T17:03:36.897Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Supercharging, <PERSON><PERSON>, <PERSON> Call", "titleUrl": "https://www.youtube.com/watch?v=Wa_wf4EzLgY", "subtitles": [{"name": "Tesla Daily", "url": "https://www.youtube.com/channel/UCgYkuL87rUwiBl7tqfN53Eg"}], "time": "2021-03-27T00:51:25.587Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Leaked Elon Musk Call Details + Tesla The World's Biggest Company?", "titleUrl": "https://www.youtube.com/watch?v=Fc-0uyVC2Ww", "subtitles": [{"name": "Tesla Daily", "url": "https://www.youtube.com/channel/UCgYkuL87rUwiBl7tqfN53Eg"}], "time": "2021-03-26T23:20:49.359Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Our Why", "titleUrl": "https://www.youtube.com/watch?v=zQrufVEaGn0", "subtitles": [{"name": "Live Learn Innovate", "url": "https://www.youtube.com/channel/UCgl49JuiO-sO0-oL0Fswr2g"}], "time": "2021-03-26T22:50:40.091Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Do You Want To... (Frozen Quarantine Parody)", "titleUrl": "https://www.youtube.com/watch?v=7h2oMz329Ow", "subtitles": [{"name": "The Holderness Family", "url": "https://www.youtube.com/channel/UCl2axinLKd00nMBW6RTASag"}], "time": "2021-03-25T14:54:11.875Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched It's Vaccination Day! - \"For The First Time In Forever\" Frozen Parody", "titleUrl": "https://www.youtube.com/watch?v=U74wUO54Sdg", "subtitles": [{"name": "The Holderness Family", "url": "https://www.youtube.com/channel/UCl2axinLKd00nMBW6RTASag"}], "time": "2021-03-25T14:49:43.091Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched It Wasn't Me", "titleUrl": "https://www.youtube.com/watch?v=sTMgX1PDGAE", "subtitles": [{"name": "<PERSON><PERSON>ggy - <PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCUqmn42LCN5JJ6-0lQl0vPg"}], "time": "2021-03-25T14:42:55.024Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Shaggy - <PERSON> ft. <PERSON><PERSON> (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=XWJrPzAUzAs", "subtitles": [{"name": "ShaggyVEVO", "url": "https://www.youtube.com/channel/UC6itD5EKLCgriSi_k2lpoxg"}], "time": "2021-03-25T14:40:23.875Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Everything Contagion got right on the Coronavirus outbreak", "titleUrl": "https://www.youtube.com/watch?v=zZOn6rrpU-Q", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCdWf7WSAedNHnvGQspdYTNw"}], "time": "2021-03-25T01:14:29.424Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Contagion (2011) Official Exclusive 1080p HD Trailer", "titleUrl": "https://www.youtube.com/watch?v=4sYSyuuLk5g", "subtitles": [{"name": "Movieclips Trailers", "url": "https://www.youtube.com/channel/UCi8e0iOVk1fEOogdfu4YgfA"}], "time": "2021-03-25T01:11:52.682Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Heart - Stairway to Heaven Led Zeppelin - Kennedy Center Honors HD", "titleUrl": "https://www.youtube.com/watch?v=2cZ_EFAmj08", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCttjzL_8yXFeeZbD-X3zrng"}], "time": "2021-03-24T23:16:15.082Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 2017 Tesla Model S AutoPilot Through Construction in Charlotte NC on 9/26/2019", "titleUrl": "https://www.youtube.com/watch?v=jnSZQ44dpyY", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-03-24T23:15:00.628Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched AC/DC - It's A Long Way To The Top LIVE On TV 1976", "titleUrl": "https://www.youtube.com/watch?v=nULs4JW3tPI", "subtitles": [{"name": "RockMusic lmL Forevah", "url": "https://www.youtube.com/channel/UCi_4ck89D4ZPxjwztKGPIgw"}], "time": "2021-03-24T23:14:54.006Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla V11 Software Preview", "titleUrl": "https://www.youtube.com/watch?v=FsLAxAUEEow", "subtitles": [{"name": "Black Tesla", "url": "https://www.youtube.com/channel/UCatID1g14HqyWufbrkgtbyg"}], "time": "2021-03-24T11:42:48.608Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Now is the time for <PERSON><PERSON>!", "titleUrl": "https://www.youtube.com/watch?v=1rRkw0UGleY", "subtitles": [{"name": "Sumo Logic, Inc.", "url": "https://www.youtube.com/channel/UCI16kViradUnvH6DiQmwdqw"}], "time": "2021-03-24T03:03:50.072Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for what's up", "titleUrl": "https://www.youtube.com/results?search_query=what%27s+up", "time": "2021-03-24T02:07:06.053Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched OAuth 2.0 and OpenID Connect (in plain English)", "titleUrl": "https://www.youtube.com/watch?v=996OiexHze0", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UC5AMiWqFVFxF1q9Ya1FuZ_Q"}], "time": "2021-03-23T20:03:30.283Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Introducing Firebase Realtime Database", "titleUrl": "https://www.youtube.com/watch?v=U5aeM5dvUpA", "subtitles": [{"name": "Firebase", "url": "https://www.youtube.com/channel/UCP4bf6IHJJQehibu6ai__cg"}], "time": "2021-03-23T13:00:36.843Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched NEW Tesla Model S Leaked", "titleUrl": "https://www.youtube.com/watch?v=z-lR7b8dFZQ", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCfv7-e6_6ZhvDL9-7Yw5OVA"}], "time": "2021-03-22T22:56:32.008Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched OAuth 2.0 and OpenID Connect (in plain English)", "titleUrl": "https://www.youtube.com/watch?v=996OiexHze0", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UC5AMiWqFVFxF1q9Ya1FuZ_Q"}], "time": "2021-03-22T12:56:00.560Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched $ALYI MODUS Releases First Ever ALYI Retro Revolt Electric Motorcycle Demo Video", "titleUrl": "https://www.youtube.com/watch?v=ZvL3Ij3bnGo", "subtitles": [{"name": "OTC PR Wire", "url": "https://www.youtube.com/channel/UCmUCdnJuqVmahYnZLjg3Ciw"}], "time": "2021-03-22T01:00:04.234Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched OAuth 2.0 and OpenID Connect (in plain English)", "titleUrl": "https://www.youtube.com/watch?v=996OiexHze0", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UC5AMiWqFVFxF1q9Ya1FuZ_Q"}], "time": "2021-03-21T18:04:12.484Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Subscribed to Jordan <PERSON>", "titleUrl": "https://www.youtube.com/channel/UCL_f53ZEJxp8TtlOkHwMV9Q", "time": "2021-03-21T16:21:44.891Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for jordan peterson", "titleUrl": "https://www.youtube.com/results?search_query=jordan+peterson", "time": "2021-03-21T16:21:27.642Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Start with why -- how great leaders inspire action | <PERSON> | TEDxPugetSound", "titleUrl": "https://www.youtube.com/watch?v=u4ZoJKF_VuA", "subtitles": [{"name": "TEDx Talks", "url": "https://www.youtube.com/channel/UCsT0YIqwnpJCM-mx7-gSA4Q"}], "time": "2021-03-20T14:11:04.796Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched [FSD Beta 8.2] Oakland - Close Calls, Pedestrians, Bicycles!", "titleUrl": "https://www.youtube.com/watch?v=antLneVlxcs", "subtitles": [{"name": "AI Addict", "url": "https://www.youtube.com/channel/UCnSt1nfVXyTyMbKhk-IaTJw"}], "time": "2021-03-20T03:29:53.215Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Before you download the FSD BETA...", "titleUrl": "https://www.youtube.com/watch?v=n4zD_vdOfhw", "subtitles": [{"name": "AI DRIVR", "url": "https://www.youtube.com/channel/UCMXzc5fADodNEdkSbHXZlpg"}], "time": "2021-03-19T21:00:59.621Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched ace ventura - loser", "titleUrl": "https://www.youtube.com/watch?v=EUholHlmxYA", "subtitles": [{"name": "wabak sindrom", "url": "https://www.youtube.com/channel/UCknDS-gXOckk7a2g19dw_3w"}], "time": "2021-03-19T17:03:50.993Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Biden Victory Cold Open - SNL", "titleUrl": "https://www.youtube.com/watch?v=vJYL4Osyipc", "subtitles": [{"name": "Saturday Night Live", "url": "https://www.youtube.com/channel/UCqFzWxSCi39LnW1JKFR3efg"}], "time": "2021-03-19T16:56:49.105Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched RSAC Launch Pad - Zero Networks", "titleUrl": "https://www.youtube.com/watch?v=jpIUQgA7a0Y", "subtitles": [{"name": "RSA Conference", "url": "https://www.youtube.com/channel/UCYzwGkfOqrevO-4TuTjPLwQ"}], "time": "2021-03-19T03:27:21.431Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Git-R-Done - <PERSON> & Larry: We've Been Thinking", "titleUrl": "https://www.youtube.com/watch?v=36E4FRSfWNo", "subtitles": [{"name": "Comedy Dynamics", "url": "https://www.youtube.com/channel/UCDUhp92NhqVc0K3jSmWhgvA"}], "time": "2021-03-18T19:20:05.349Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON>: From Flunking College, to Mega Comedy Success | Full Documentary | Biography", "titleUrl": "https://www.youtube.com/watch?v=zK-MdPm4CSo", "subtitles": [{"name": "Biography", "url": "https://www.youtube.com/channel/UCiCPv2sV_D3FqMRzzUFA2Fg"}], "time": "2021-03-18T19:20:02.967Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Only In America with <PERSON> the Cable Guy - Git-R-Done | History", "titleUrl": "https://www.youtube.com/watch?v=4TU-li1BqZ4", "subtitles": [{"name": "HISTORY", "url": "https://www.youtube.com/channel/UC9MAhZQQd9egwWCxrwSIsJQ"}], "time": "2021-03-18T19:17:14.296Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched KITT Speaks For The First Time | Knight Rider", "titleUrl": "https://www.youtube.com/watch?v=dANY3uk7lxc", "subtitles": [{"name": "Knight Rider Official", "url": "https://www.youtube.com/channel/UCvPjQRtzIQSfIHj0VD9ymvw"}], "time": "2021-03-18T17:44:21.977Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Big Bang Theory Season 5 Behind The Scenes", "titleUrl": "https://www.youtube.com/watch?v=TmvWgcXVLcQ", "subtitles": [{"name": "paaaaammmmmiiiieeeee", "url": "https://www.youtube.com/channel/UCiaBsLeYvFt2G1wjw7fItoA"}], "time": "2021-03-18T15:10:16.486Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Big Bang Theory - Sheldon On HP Customer Service", "titleUrl": "https://www.youtube.com/watch?v=zxSJZ5aGTTo", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-03-18T15:09:47.068Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for hp customer support", "titleUrl": "https://www.youtube.com/results?search_query=hp+customer+support", "time": "2021-03-18T14:54:48.103Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 1982 KITT From \"Knight Rider\" - <PERSON>'s Garage", "titleUrl": "https://www.youtube.com/watch?v=3YKtQrYNKPg", "subtitles": [{"name": "<PERSON>'s Garage", "url": "https://www.youtube.com/channel/UCQMELFlXQL38KPm8kM-4Adg"}], "time": "2021-03-18T12:47:40.710Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Dew Point", "titleUrl": "https://www.youtube.com/watch?v=fhqbJGF3sJk", "subtitles": [{"name": "TDarcyPhysics", "url": "https://www.youtube.com/channel/UC0vUQrZ_goDSxMLlVg4uNbA"}], "time": "2021-03-17T02:13:22.302Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched What is dew point?", "titleUrl": "https://www.youtube.com/watch?v=rAgli8giur4", "subtitles": [{"name": "cleveland.com", "url": "https://www.youtube.com/channel/UCJkc1COQO0WiZ2CI7dRdwAQ"}], "time": "2021-03-17T02:11:44.139Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Papa <PERSON> - Last Resort (Squeaky-clean Version)", "titleUrl": "https://www.youtube.com/watch?v=j0lSpNtjPM8", "subtitles": [{"name": "PapaRoachVEVO", "url": "https://www.youtube.com/channel/UCz4TqVes6nggOlTSqKfvdkA"}], "time": "2021-03-17T00:50:12.526Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Papa <PERSON> - Last Resort (Squeaky-clean Version)", "titleUrl": "https://www.youtube.com/watch?v=j0lSpNtjPM8", "subtitles": [{"name": "PapaRoachVEVO", "url": "https://www.youtube.com/channel/UCz4TqVes6nggOlTSqKfvdkA"}], "time": "2021-03-15T13:25:47.150Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Eminem - <PERSON> God (Audio)", "titleUrl": "https://www.youtube.com/watch?v=S7cQ3b0iqLo", "subtitles": [{"name": "EminemMusic", "url": "https://www.youtube.com/channel/UCfM3zsQsOnfWNUppiycmBuw"}], "time": "2021-03-15T13:25:37.703Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched CEO Test Drive | Lucid Air | Lucid Motors", "titleUrl": "https://www.youtube.com/watch?v=LM6x_jSKixg", "subtitles": [{"name": "Lucid Motors", "url": "https://www.youtube.com/channel/UCxBheRQeJNTBYAsPAHhaBhA"}], "time": "2021-03-15T03:12:37.741Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Elon Musk on Hydrogen Fuel Cells", "titleUrl": "https://www.youtube.com/watch?v=yFPnT-DCBVs", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC5Ej-McwoFhgP8ah5L5FvRQ"}], "time": "2021-03-14T20:21:42.031Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Podcast: Tesla prices, Plaid+ delay, Audi Q4 e-tron, Volvo C40, more", "titleUrl": "https://www.youtube.com/watch?v=3CdHzSXSouw", "subtitles": [{"name": "Electrek.co", "url": "https://www.youtube.com/channel/UCcOIZzJgLCyMPILY7-1Vsdg"}], "time": "2021-03-13T15:46:40.811Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Nikola Semi Pilot Factory Construction Site March 11th, 2021 | Drone Footage 9:30 AM", "titleUrl": "https://www.youtube.com/watch?v=Blmb9QxTlbo", "subtitles": [{"name": "Bear's Workshop", "url": "https://www.youtube.com/channel/UCFu8aWIBE8JXMnsYzthpTDQ"}], "time": "2021-03-13T15:44:08.929Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Create your own explainer video with simpleshow video maker", "titleUrl": "https://www.youtube.com/watch?v=KVjIz-TxADg", "subtitles": [{"name": "simpleshow", "url": "https://www.youtube.com/channel/UCyYOgVNW--SNcRHtnnZaXvQ"}], "time": "2021-03-12T21:35:10.732Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Foo Fighters - The Pretender", "titleUrl": "https://www.youtube.com/watch?v=SBjQ9tuuTJQ", "subtitles": [{"name": "foofightersVEVO", "url": "https://www.youtube.com/channel/UCGRjJrpD2bmk9Ilq6nq80qg"}], "time": "2021-03-12T04:47:34.295Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> Jovi - Always (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=9BMwcO6_hyA", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-03-12T04:46:38.987Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Offspring - The Kids Aren't Alright (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=7iNbnineUCI", "subtitles": [{"name": "OffspringVEVO", "url": "https://www.youtube.com/channel/UCF3EnnuLjeab3r8l1k2rV9g"}], "time": "2021-03-12T04:45:13.384Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Papa <PERSON> - Last Resort (Squeaky-clean Version)", "titleUrl": "https://www.youtube.com/watch?v=j0lSpNtjPM8", "subtitles": [{"name": "PapaRoachVEVO", "url": "https://www.youtube.com/channel/UCz4TqVes6nggOlTSqKfvdkA"}], "time": "2021-03-12T04:41:58.821Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - I'll Be There For You (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=mh8MIp2FOhc", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-03-12T04:41:09.341Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - Keep The Faith", "titleUrl": "https://www.youtube.com/watch?v=eZQyVUTcpM4", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-03-12T04:32:53.181Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - Bed Of Roses (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=NvR60Wg9R7Q", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-03-12T04:20:03.266Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> Jovi - You Give Love A Bad Name (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=KrZHPOeOxQQ", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-03-12T04:16:11.716Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> Jo<PERSON> - Livin' On A Prayer (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=lDK9QqIzhwk", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-03-12T04:12:01.919Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Nirvana - Smells Like Teen Spirit (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=hTWKbfoikeg", "subtitles": [{"name": "NirvanaVEVO", "url": "https://www.youtube.com/channel/UCzGrGrvf9g8CVVzh_LvGf-g"}], "time": "2021-03-12T04:07:22.599Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The White Stripes - Seven Nation Army (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=0J2QdDbelmY", "subtitles": [{"name": "The White Stripes", "url": "https://www.youtube.com/channel/UC0sQemK7pgX5fYy0k1MFsHg"}], "time": "2021-03-12T03:58:52.006Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Twisted Sister - We're Not Gonna Take it (Extended Version) (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=V9AbeALNVkk", "subtitles": [{"name": "Twisted Sister", "url": "https://www.youtube.com/channel/UCRay0Axn67v8-nm6n91uBsg"}], "time": "2021-03-12T03:52:01.501Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - It's My Life (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=vx2u5uUu3DE", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-03-12T03:47:33.603Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched PPE  Exchange HD - Animated Explainer by NextGen Animations", "titleUrl": "https://www.youtube.com/watch?v=jYPnvVtQYS4", "subtitles": [{"name": "NextGen Animations", "url": "https://www.youtube.com/channel/UCCNdSvoQiFe9gAuBM47wPFg"}], "time": "2021-03-10T01:28:20.271Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON>’s prediction for the future of energy in Australia | 60 Minutes Australia", "titleUrl": "https://www.youtube.com/watch?v=PZz2r9j1Lfo", "subtitles": [{"name": "60 Minutes Australia", "url": "https://www.youtube.com/channel/UC0L1suV8pVgO4pCAIBNGx5w"}], "time": "2021-03-07T04:38:15.379Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Inside Azure Datacenters with <PERSON>: Part 1 | Ignite 2020", "titleUrl": "https://www.youtube.com/watch?v=v990MJXuj8Q", "subtitles": [{"name": "Microsoft Ignite", "url": "https://www.youtube.com/channel/UCrhJmfAGQ5K81XQ8_od1iTg"}], "time": "2021-03-06T06:05:17.387Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla Update 2021.4.11 // WARNING!!! This one will put you to sleep.", "titleUrl": "https://www.youtube.com/watch?v=ZgChsuquDo0", "subtitles": [{"name": "<PERSON>f <PERSON><PERSON>", "url": "https://www.youtube.com/channel/UC8gsHIK0Lja2TcMSPzbOTPg"}], "time": "2021-03-04T12:15:31.427Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Mastering Chaos - A Netflix Guide to Microservices", "titleUrl": "https://www.youtube.com/watch?v=CZ3wIuvmHeM", "subtitles": [{"name": "InfoQ", "url": "https://www.youtube.com/channel/UCkQX1tChV7Z7l1LFF4L9j_g"}], "time": "2021-03-02T23:29:19.386Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Sugar Hollow Solar", "titleUrl": "https://www.youtube.com/watch?v=EGaZKwPGGhQ", "subtitles": [{"name": "Sugar Hollow Solar", "url": "https://www.youtube.com/channel/UCi9d1WtEX71ctN7aI0Rs_WA"}], "time": "2021-03-02T02:15:10.023Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Controller Rumble is not enough to feel the game, Just Rumble Everything!", "titleUrl": "https://www.youtube.com/watch?v=fxmLD8y0RNQ", "subtitles": [{"name": "Teenenggr", "url": "https://www.youtube.com/channel/UCNolGmzBR60IZfS9z7LlNEw"}], "time": "2021-03-01T14:59:07.765Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla battery powered house and it WORKS (from start to finish)", "titleUrl": "https://www.youtube.com/watch?v=qpPYkqpe-Ms", "subtitles": [{"name": "oldmilwaukee100", "url": "https://www.youtube.com/channel/UCdWiDYegKchRbqjPAbXt-jA"}], "time": "2021-02-28T21:30:02.175Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - What Is Life (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=fiH9edd25Bc", "subtitles": [{"name": "GeorgeHarrisonVEVO", "url": "https://www.youtube.com/channel/UCS1YGiJZUVUTMqYADYG9frw"}], "time": "2021-02-28T18:33:59.357Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Mastering Chaos - A Netflix Guide to Microservices", "titleUrl": "https://www.youtube.com/watch?v=CZ3wIuvmHeM", "subtitles": [{"name": "InfoQ", "url": "https://www.youtube.com/channel/UCkQX1tChV7Z7l1LFF4L9j_g"}], "time": "2021-02-28T07:25:24.385Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Watch NASA’s Perseverance Rover Land on Mars!", "titleUrl": "https://www.youtube.com/watch?v=gm0b_ijaYMQ", "subtitles": [{"name": "NASA", "url": "https://www.youtube.com/channel/UCLA_DiR1FfKNvjuUpBHmylQ"}], "time": "2021-02-27T17:35:25.318Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched how we write/review code in big tech companies", "titleUrl": "https://www.youtube.com/watch?v=rR4n-0KYeKQ", "subtitles": [{"name": "Joma Tech", "url": "https://www.youtube.com/channel/UCV0qA-eDDICsRR9rPcnG7tw"}], "time": "2021-02-27T17:29:57.182Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Vapors - Turning Japanese", "titleUrl": "https://www.youtube.com/watch?v=IWWwM2wwMww", "subtitles": [{"name": "missiongoran2", "url": "https://www.youtube.com/channel/UCCs1LFJx0-BUog3bzQSPYRw"}], "time": "2021-02-26T04:16:39.456Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched English Google SEO office-hours from February 19, 2021", "titleUrl": "https://www.youtube.com/watch?v=zCV6tEt3w0k", "subtitles": [{"name": "Google Search Central", "url": "https://www.youtube.com/channel/UCWf2ZlNsCGDS89VBF_awNvA"}], "time": "2021-02-24T05:52:25.698Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched metaKnyts Start Engine Promo Reel", "titleUrl": "https://www.youtube.com/watch?v=jm3eKSWeT-o", "subtitles": [{"name": "metaMe", "url": "https://www.youtube.com/channel/UCcuKD_TNBKwht0xUdVArJRA"}], "time": "2021-02-24T03:47:15.076Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched New wearable device turns the body into a battery", "titleUrl": "https://www.youtube.com/watch?v=hexScHvEFwQ", "subtitles": [{"name": "University of Colorado Boulder", "url": "https://www.youtube.com/channel/UCgCPZtGaEAwYpcDPlVwV--Q"}], "time": "2021-02-24T03:12:23.221Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Joe Rogan Experience #1169 - <PERSON><PERSON>", "titleUrl": "https://www.youtube.com/watch?v=ycPr5-27vSI", "subtitles": [{"name": "PowerfulJRE", "url": "https://www.youtube.com/channel/UCzQUP1qoWDoEbmsQxvdjxgQ"}], "time": "2021-02-21T21:25:44.402Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Plastic bricks in Kenya - NZAMBI MATEE - Young Champion of the Earth 2020 for Africa", "titleUrl": "https://www.youtube.com/watch?v=QbZKP4UAtL8", "subtitles": [{"name": "UN Environment Programme", "url": "https://www.youtube.com/channel/UC9V3x9HelwEk3Z6EknB_1Cg"}], "time": "2021-02-21T13:18:12.773Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched BITCOIN EXPLAINED (BC Explained ep 1)", "titleUrl": "https://www.youtube.com/watch?v=YHjYt6Jm5j8", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCiTGrzvVISsgE_DluwC6r6g"}], "time": "2021-02-21T04:09:31.595Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Conversation with <PERSON> - Why he Sold his Tesla Shares, but <PERSON><PERSON><PERSON> in the Long Term.", "titleUrl": "https://www.youtube.com/watch?v=rjcqGsqVwyQ", "subtitles": [{"name": "Spark Spread", "url": "https://www.youtube.com/channel/UCMEgks5J4lvyhUR5nwJOrpw"}], "time": "2021-02-20T22:09:49.022Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched I'll Be Back: A <PERSON>", "titleUrl": "https://www.youtube.com/watch?v=ELgA3S996Ng", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCX137CBR6Uq7MW_vGsDWOUw"}], "time": "2021-02-20T20:54:01.905Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla Solar Roof - What happens when it snows?", "titleUrl": "https://www.youtube.com/watch?v=f_GHVSL35-k", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCPQDjGEZZvINPYgZik5cUqw"}], "time": "2021-02-20T13:05:42.550Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched FIRST 2021 TESLA MODEL S REFRESH/PLAID SPY SHOTS! Chrome Delete, Wide Body, Rear Diffuser, & Wheels!", "titleUrl": "https://www.youtube.com/watch?v=Ugi7YoMF3bk", "subtitles": [{"name": "The Kilowatts", "url": "https://www.youtube.com/channel/UCD_QsRZod1gf2Ghm70_8IUA"}], "time": "2021-02-20T04:14:10.630Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - self-help (Entry 3.06)", "titleUrl": "https://www.youtube.com/watch?v=WzEXYNf3SEU", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCsx1BdgxAtwHfNUt1fS4eEA"}], "time": "2021-02-19T20:42:55.802Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched george carlin on roosters, self help and motivation", "titleUrl": "https://www.youtube.com/watch?v=KTETZy18pwk", "subtitles": [{"name": "jesse taplin", "url": "https://www.youtube.com/channel/UCvoBOA1f0Xar5mFsA3IQsxA"}], "time": "2021-02-19T20:42:39.629Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Self-Help Books - <PERSON>", "titleUrl": "https://www.youtube.com/watch?v=BCsM35H9TFA", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCXZzXEagkm7Pc2mr1udqKHQ"}], "time": "2021-02-19T20:41:54.606Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Automatic pool stick vs. strangers", "titleUrl": "https://www.youtube.com/watch?v=vsTTXYxydOE", "subtitles": [{"name": "Stuff Made Here", "url": "https://www.youtube.com/channel/UCj1VqrHhDte54oLgPG4xpuQ"}], "time": "2021-02-19T14:01:13.579Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Spies Like Us (1985) - <PERSON>, Doctor <PERSON> (4/8) | Movieclips", "titleUrl": "https://www.youtube.com/watch?v=hoe24aSvLtw", "subtitles": [{"name": "Movieclips", "url": "https://www.youtube.com/channel/UC3gNmTGu-TTbFPpfSs5kNkg"}], "time": "2021-02-19T13:55:52.760Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Spies Like Us (1985) - Going Out with a Bang Scene (8/8) | Movieclips", "titleUrl": "https://www.youtube.com/watch?v=T5rzOU-4Bkc", "subtitles": [{"name": "Movieclips", "url": "https://www.youtube.com/channel/UC3gNmTGu-TTbFPpfSs5kNkg"}], "time": "2021-02-19T13:54:15.178Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Spies Like Us (1985) Official Trailer - Ch<PERSON><PERSON>, <PERSON>", "titleUrl": "https://www.youtube.com/watch?v=VEqvqbjzWuU", "subtitles": [{"name": "Movieclips Classic Trailers", "url": "https://www.youtube.com/channel/UCTCjFFoX1un-j7ni4B6HJ3Q"}], "time": "2021-02-19T13:52:23.186Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Learn useRef in 11 Minutes", "titleUrl": "https://www.youtube.com/watch?v=t2ypzz6gJm0", "subtitles": [{"name": "Web Dev Simplified", "url": "https://www.youtube.com/channel/UCFbNIlppjAuEX4znoulh0Cw"}], "time": "2021-02-19T02:34:11.151Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Aston Martin Bulldog | William Towns | Retro Car review | Wheels | 1979", "titleUrl": "https://www.youtube.com/watch?v=afZY9YSPcmA", "subtitles": [{"name": "ThamesTv", "url": "https://www.youtube.com/channel/UChXQL4MqC4ihSVg3SFiszDg"}], "time": "2021-02-18T04:45:39.808Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched How to Build a Mobile App Using AWS", "titleUrl": "https://www.youtube.com/watch?v=p7arftIf6U4", "subtitles": [{"name": "Amazon Web Services", "url": "https://www.youtube.com/channel/UCd6MoB9NC6uYN2grvUNT-Zg"}], "time": "2021-02-18T03:49:10.150Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched GoFundMe Charity - Start a campaign", "titleUrl": "https://www.youtube.com/watch?v=e6SgQwGOOxU", "subtitles": [{"name": "GoFundMe Charity", "url": "https://www.youtube.com/channel/UCqWS7My096-7QPwaG5bBVgg"}], "time": "2021-02-18T01:32:13.763Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - Predicting <PERSON><PERSON> in 2011", "titleUrl": "https://www.youtube.com/watch?v=zRt94u1lQcE", "subtitles": [{"name": "EV Stock Channel", "url": "https://www.youtube.com/channel/UCMfEjqHQS4u8W5etV0uAG_A"}], "time": "2021-02-15T18:15:08.913Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Gorilla Glue - SNL", "titleUrl": "https://www.youtube.com/watch?v=eT6NUxmUrvk", "subtitles": [{"name": "Saturday Night Live", "url": "https://www.youtube.com/channel/UCqFzWxSCi39LnW1JKFR3efg"}], "time": "2021-02-15T01:49:57.979Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched ClickTips - Bug Reporting", "titleUrl": "https://www.youtube.com/watch?v=Yakmb1rGqZI", "subtitles": [{"name": "ClickUp", "url": "https://www.youtube.com/channel/UCJC7egHqghye211WgTuXMAw"}], "time": "2021-02-14T19:44:53.458Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Chicago - Stay The Night (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=5LTWwkBNilI", "subtitles": [{"name": "Chicago Band", "url": "https://www.youtube.com/channel/UCK0TuNeYqg9oYGSksX-jNkg"}], "time": "2021-02-14T16:27:02.383Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - Predicting <PERSON><PERSON> in 2011", "titleUrl": "https://www.youtube.com/watch?v=zRt94u1lQcE", "subtitles": [{"name": "EV Stock Channel", "url": "https://www.youtube.com/channel/UCMfEjqHQS4u8W5etV0uAG_A"}], "time": "2021-02-14T01:59:35.598Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched World's largest solar farm could help power Singapore from NT desert | ABC News", "titleUrl": "https://www.youtube.com/watch?v=ESWBEQzxkPg", "subtitles": [{"name": "ABC News (Australia)", "url": "https://www.youtube.com/channel/UCVgO39Bk5sMo66-6o6Spn6Q"}], "time": "2021-02-13T12:54:02.832Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Spaceballs (4/11) Movie CLIP - Ludicrous Speed (1987) HD", "titleUrl": "https://www.youtube.com/watch?v=NAWL8ejf2nM", "subtitles": [{"name": "Movieclips", "url": "https://www.youtube.com/channel/UC3gNmTGu-TTbFPpfSs5kNkg"}], "time": "2021-02-13T02:06:03.452Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Eminem - <PERSON> God (Audio)", "titleUrl": "https://www.youtube.com/watch?v=S7cQ3b0iqLo", "subtitles": [{"name": "EminemMusic", "url": "https://www.youtube.com/channel/UCfM3zsQsOnfWNUppiycmBuw"}], "time": "2021-02-11T16:32:30.614Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched dotScale 2013 - <PERSON> - Why we built ElasticSearch", "titleUrl": "https://www.youtube.com/watch?v=fEsmydn747c", "subtitles": [{"name": "dotconferences", "url": "https://www.youtube.com/channel/UCSRhwaM00ay0fasnsw6EXKA"}], "time": "2021-02-11T05:00:25.858Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Ambee TechStars Demo Day Pitch", "titleUrl": "https://www.youtube.com/watch?v=mjizayQvYRg", "subtitles": [{"name": "Ambee", "url": "https://www.youtube.com/channel/UCRssDNRLFa96efJ8l-IsDdA"}], "time": "2021-02-11T04:29:12.961Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> Standup Special - \"Good Deal\" Trailer", "titleUrl": "https://www.youtube.com/watch?v=vdkjfvcx8ak", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCi2rNogXeswPLmcHxl7tHPA"}], "time": "2021-02-10T18:14:34.807Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Ambee TechStars Demo Day Pitch", "titleUrl": "https://www.youtube.com/watch?v=mjizayQvYRg", "subtitles": [{"name": "Ambee", "url": "https://www.youtube.com/channel/UCRssDNRLFa96efJ8l-IsDdA"}], "time": "2021-02-09T20:38:20.208Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched dotScale 2013 - <PERSON> - Why we built ElasticSearch", "titleUrl": "https://www.youtube.com/watch?v=fEsmydn747c", "subtitles": [{"name": "dotconferences", "url": "https://www.youtube.com/channel/UCSRhwaM00ay0fasnsw6EXKA"}], "time": "2021-02-09T04:43:44.302Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Ambee TechStars Demo Day Pitch", "titleUrl": "https://www.youtube.com/watch?v=mjizayQvYRg", "subtitles": [{"name": "Ambee", "url": "https://www.youtube.com/channel/UCRssDNRLFa96efJ8l-IsDdA"}], "time": "2021-02-08T20:32:31.732Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched LEAKED Tesla Model 2 in 2021?", "titleUrl": "https://www.youtube.com/watch?v=UmG85aK0LNI", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCfv7-e6_6ZhvDL9-7Yw5OVA"}], "time": "2021-02-06T23:40:13.406Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Talking Tech with <PERSON><PERSON>!", "titleUrl": "https://www.youtube.com/watch?v=MevKTPN4ozw", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCBJycsmduvYEL83R_U4JriQ"}], "time": "2021-02-05T07:06:03.838Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Pedestrian detection put to the test in Tesla's FSD BETA", "titleUrl": "https://www.youtube.com/watch?v=YxxYyetcxXk", "subtitles": [{"name": "AI DRIVR", "url": "https://www.youtube.com/channel/UCMXzc5fADodNEdkSbHXZlpg"}], "time": "2021-02-03T05:00:24.611Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON> - AI for Full-Self Driving at Tesla", "titleUrl": "https://www.youtube.com/watch?v=hx7BXih7zx8", "subtitles": [{"name": "Matroid", "url": "https://www.youtube.com/channel/UCR85FcguqcxLQ7XEg1byAtQ"}], "time": "2021-02-03T04:58:56.641Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Py<PERSON><PERSON><PERSON> at Tesla - Andrej Karpathy, Tesla", "titleUrl": "https://www.youtube.com/watch?v=oBklltKXtDE", "subtitles": [{"name": "PyTorch", "url": "https://www.youtube.com/channel/UCWXI5YeOsh03QvJ59PMaXFw"}], "time": "2021-02-03T04:47:26.671Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched What happens after you spit in the tube?", "titleUrl": "https://www.youtube.com/watch?v=saNeGk9GY6A", "subtitles": [{"name": "60 Minutes", "url": "https://www.youtube.com/channel/UCsN32BtMd0IoByjJRNF12cw"}], "time": "2021-02-02T02:28:32.625Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Dimplex Danville - getting the best from your Opti-myst electric fire", "titleUrl": "https://www.youtube.com/watch?v=-BpF6jUKrpY", "subtitles": [{"name": "Dimplex UK", "url": "https://www.youtube.com/channel/UCxfDbHdsYVUBLKjojL5WurQ"}], "time": "2021-02-01T01:39:04.705Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON>ti-myst Explained", "titleUrl": "https://www.youtube.com/watch?v=kTCABqToFJQ", "subtitles": [{"name": "Dimplex UK", "url": "https://www.youtube.com/channel/UCxfDbHdsYVUBLKjojL5WurQ"}], "time": "2021-02-01T01:36:05.150Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for dimplex electric fireplace", "titleUrl": "https://www.youtube.com/results?search_query=dimplex+electric+fireplace", "time": "2021-02-01T01:35:58.391Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Fireplace - 4K HDR 60fps - 2 hours - no loop", "titleUrl": "https://www.youtube.com/watch?v=tW18zfk73DU", "subtitles": [{"name": "Aura Video Art", "url": "https://www.youtube.com/channel/UCYKp3v9YOcv6UpFvrM-dzgw"}], "time": "2021-01-30T17:09:45.923Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The World's Most Realistic Electric Fireplace", "titleUrl": "https://www.youtube.com/watch?v=EqftKJv_eKc", "subtitles": [{"name": "Woodland Direct Inc", "url": "https://www.youtube.com/channel/UCRTlZRXFVlZGkV_KjzuP5QA"}], "time": "2021-01-30T17:08:44.929Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Assembling <PERSON><PERSON>ame <PERSON> for Installation", "titleUrl": "https://www.youtube.com/watch?v=bHpWGcdK9LY", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCz3KEoC1VEJdK60TFPpBtUQ"}], "time": "2021-01-30T14:32:56.531Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Worst Wifi Password Ever", "titleUrl": "https://www.youtube.com/watch?v=bLE7zsJk4AI", "subtitles": [{"name": "RocketJump", "url": "https://www.youtube.com/channel/UCDsO-0Yo5zpJk575nKXgMVA"}], "time": "2021-01-28T20:29:06.400Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for wifi password lowercase", "titleUrl": "https://www.youtube.com/results?search_query=wifi+password+lowercase", "time": "2021-01-28T20:28:56.761Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for wifi password", "titleUrl": "https://www.youtube.com/results?search_query=wifi+password", "time": "2021-01-28T20:28:41.997Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Perovskite Solar Modules in Action", "titleUrl": "https://www.youtube.com/watch?v=XSFvcRrstHE", "subtitles": [{"name": "SciTech Daily", "url": "https://www.youtube.com/channel/UC6E1FUUq5SuMxIFndY0yUEg"}], "time": "2021-01-28T02:16:14.104Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla Q4 and full year 2020 Financial Results and Q&A Webcast", "titleUrl": "https://www.youtube.com/watch?v=8GczshcYSAc", "subtitles": [{"name": "Tesla", "url": "https://www.youtube.com/channel/UC5WjFrtBdufl6CZojX3D8dQ"}], "time": "2021-01-27T23:28:29.622Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON> - Nvidia F_ck You!", "titleUrl": "https://www.youtube.com/watch?v=IVpOyKCNZYw", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCK73ahFgN7exzqFkBKhxk_g"}], "time": "2021-01-26T14:08:48.487Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> ♕ Transformation From A Child To 62 Years OLD", "titleUrl": "https://www.youtube.com/watch?v=M_NWZdTmYOw", "subtitles": [{"name": "Top Celebs Tube", "url": "https://www.youtube.com/channel/UCgRNiQuOiTbgmIvrQA7LGfQ"}], "time": "2021-01-25T23:57:06.952Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched NIO ET7 first look; new NIO sedan trailer: launch at NIO Day 2020 [FHD]", "titleUrl": "https://www.youtube.com/watch?v=vYNOO5Ew0Hg", "subtitles": [{"name": "Driving China", "url": "https://www.youtube.com/channel/UCYPrwLbvdgUxBpI-pSLnX9g"}], "time": "2021-01-25T14:35:18.258Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla Model Y Third Row With People", "titleUrl": "https://www.youtube.com/watch?v=rP0QPtkdAJg", "subtitles": [{"name": "Burn Hard Zen", "url": "https://www.youtube.com/channel/UC-9N2GrGJ5SxEs6YAObXIpg"}], "time": "2021-01-24T19:16:46.257Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON>   Emergency Room HD", "titleUrl": "https://www.youtube.com/watch?v=KPrSHrIvWI4", "subtitles": [{"name": "PCC320", "url": "https://www.youtube.com/channel/UCvd4GQQB8g6Un3kXNpP7vjA"}], "time": "2021-01-23T01:22:19.694Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for brian regan emergency room", "titleUrl": "https://www.youtube.com/results?search_query=brian+regan+emergency+room", "time": "2021-01-23T01:22:09.133Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Europe - The Final Countdown (Official Video)", "titleUrl": "https://www.youtube.com/watch?v=9jK-NcRmVcw", "subtitles": [{"name": "EuropeVEVO", "url": "https://www.youtube.com/channel/UCnzCV_PxVZ-hsOrTy2NGxJw"}], "time": "2021-01-22T19:36:04.229Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Spaceballs (4/11) Movie CLIP - Ludicrous Speed (1987) HD", "titleUrl": "https://www.youtube.com/watch?v=NAWL8ejf2nM", "subtitles": [{"name": "Movieclips", "url": "https://www.youtube.com/channel/UC3gNmTGu-TTbFPpfSs5kNkg"}], "time": "2021-01-21T15:07:14.786Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> expecting a round ball but gets football", "titleUrl": "https://www.youtube.com/watch?v=oeYbDcv5E3M", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-21T01:18:03.148Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Puppy Dog Learning To Swim In A Creek", "titleUrl": "https://www.youtube.com/watch?v=G3NYClgyIXc", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-21T01:16:44.723Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Rope Swing On The Neuse River North Carolina", "titleUrl": "https://www.youtube.com/watch?v=7XgCm_f9LCE", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-21T01:15:55.232Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Teasing Tired Puppy Dog With Sleep Deprivation", "titleUrl": "https://www.youtube.com/watch?v=YkKlS_91gn8", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-21T01:14:34.582Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Puppy Dog Really Wants To Go For A Run", "titleUrl": "https://www.youtube.com/watch?v=lIc4hR5SiEw", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-21T01:13:31.518Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Crazy Happy Dog", "titleUrl": "https://www.youtube.com/watch?v=zPpGJ8veen0", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-21T01:12:12.753Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> Drops A Big Surprise! - Autoline After Hours 539", "titleUrl": "https://www.youtube.com/watch?v=HZ19jzO3_Lo", "subtitles": [{"name": "Autoline Network", "url": "https://www.youtube.com/channel/UCoBaFUmLRgc_-d-rkBCFyfQ"}], "time": "2021-01-21T01:12:02.099Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON> Doing Whatever It Takes To Conquer Fear And Get The Prize", "titleUrl": "https://www.youtube.com/watch?v=rmUdIDAUgS4", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-21T01:10:33.209Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Inauguration of <PERSON> and <PERSON><PERSON> | Jan. 20th, 2021", "titleUrl": "https://www.youtube.com/watch?v=C-qYgs_yOXA", "subtitles": [{"name": "Biden Inaugural Committee", "url": "https://www.youtube.com/channel/UCUACercYthJAgyf1JAc2EhQ"}], "time": "2021-01-20T17:25:48.831Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Wings At the Speed of Sound  Full Album", "titleUrl": "https://www.youtube.com/watch?v=1Ohof_7oYjM", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCGaFc9Z5UCanRbps3EQ4MGg"}], "time": "2021-01-20T05:18:56.115Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched I Like It Chevy Chase", "titleUrl": "https://www.youtube.com/watch?v=IHAOnaoUuUg", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCcUdhlh3HBbipRsGfBt19_Q"}], "time": "2021-01-19T23:08:29.198Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Monty Python and the Holy Grail - Witch Scene", "titleUrl": "https://www.youtube.com/watch?v=rf71YotfykQ", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCjn0nUESRlWe2Mke-xWdSDw"}], "time": "2021-01-19T18:47:17.820Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched DMX - X Gon Give To Ya (Deadpool Song) [Official Music Video] Free Download HD", "titleUrl": "https://www.youtube.com/watch?v=OH_Xf35mzLA", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCyCwbRB-f-G5J-telTk9X1w"}], "time": "2021-01-19T14:41:08.477Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - If I Knew Then What I Know Now", "titleUrl": "https://www.youtube.com/watch?v=ye5EFc89vDw", "subtitles": [{"name": "PopStirizE", "url": "https://www.youtube.com/channel/UCumxFDEJIiMLM4LDZbmSL1g"}], "time": "2021-01-19T02:39:33.504Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Arcimoto 2020 Q3 Earnings Webinar", "titleUrl": "https://www.youtube.com/watch?v=axsiwiuZKec", "subtitles": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCkGZRaxqQDvK05MPuZLkB3w"}], "time": "2021-01-18T23:20:24.010Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Visited http://arcimoto.com/ir", "titleUrl": "https://www.google.com/url?q=http://arcimoto.com/ir&usg=AFQjCNG3UhT1Xq7jT4Je7lD5dcVjk3iqpA", "time": "2021-01-18T23:20:11.592Z", "products": ["YouTube"], "details": [{"name": "From Google Ads"}]}, {"header": "YouTube", "title": "Watched FSD Beta 48.35.1 (build 10) 9 minute drive with zero disengagements", "titleUrl": "https://www.youtube.com/watch?v=rWS9jjhLYSM", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCEkT8ek_h9_zmpZbaccq1Vw"}], "time": "2021-01-18T23:01:50.256Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Holy Grail - Killer Bunny", "titleUrl": "https://www.youtube.com/watch?v=XcxKIJTb3Hg", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCusgicyeT-Uaqfq_fTtelnw"}], "time": "2021-01-17T23:06:39.691Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Big Bang Theory - Sheldon On HP Customer Service", "titleUrl": "https://www.youtube.com/watch?v=zxSJZ5aGTTo", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2021-01-17T21:22:32.389Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for killer rabbit monty python", "titleUrl": "https://www.youtube.com/results?search_query=killer+rabbit+monty+python", "time": "2021-01-17T19:23:55.809Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON>-Holy Hand Grenade", "titleUrl": "https://www.youtube.com/watch?v=xOrgLj9lOwk", "subtitles": [{"name": "HopelessRomantic27", "url": "https://www.youtube.com/channel/UCJUbatjbaxh37BVyRMsM2yg"}], "time": "2021-01-17T19:21:31.557Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON>: <PERSON><PERSON>'s Lead (What Ford & GM Need To Do NOW)", "titleUrl": "https://www.youtube.com/watch?v=pGjAS_dn1V0", "subtitles": [{"name": "Solving The Money Problem", "url": "https://www.youtube.com/channel/UCagiBBx1prefrlsDzDxuA9A"}], "time": "2021-01-17T08:53:12.887Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> double clutches and dunks it while evading AD", "titleUrl": "https://www.youtube.com/watch?v=NOKGQewt6ck", "subtitles": [{"name": "MLG Highlights", "url": "https://www.youtube.com/channel/UC-XWpctw55Q6b_AHo8rkJgw"}], "time": "2021-01-17T08:51:52.749Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Why Buffett, Lynch & Munger All HATE Diversification (investing)", "titleUrl": "https://www.youtube.com/watch?v=EAib-EqU6d8", "subtitles": [{"name": "Solving The Money Problem", "url": "https://www.youtube.com/channel/UCagiBBx1prefrlsDzDxuA9A"}], "time": "2021-01-17T05:28:40.714Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON>ris - Move Bitch Get Out Da Way (HQ)", "titleUrl": "https://www.youtube.com/watch?v=cEuU64Zt4B0", "subtitles": [{"name": "ZLife", "url": "https://www.youtube.com/channel/UCJIkOWKlPVdVrG4Glk31YwQ"}], "time": "2021-01-17T01:27:41.085Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Rob Maurer: What every Tesla investor needs to know in 2021", "titleUrl": "https://www.youtube.com/watch?v=aeS82ywtEU4", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCr8NGM9PL2I5ToaKKbcdmYw"}], "time": "2021-01-16T06:02:33.242Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Wings At the Speed of Sound  Full Album", "titleUrl": "https://www.youtube.com/watch?v=1Ohof_7oYjM", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCGaFc9Z5UCanRbps3EQ4MGg"}], "time": "2021-01-16T03:39:57.748Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for paul mccartney wings at the speed of sound ", "titleUrl": "https://www.youtube.com/results?search_query=paul+mccartney+wings+at+the+speed+of+sound+", "time": "2021-01-16T03:39:47.714Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 'Junior's Farm' - PaulMcCartney.com Track of the Week", "titleUrl": "https://www.youtube.com/watch?v=5Jpc9oZynyw", "subtitles": [{"name": "PAUL McCARTNEY", "url": "https://www.youtube.com/channel/UCvGnJy9RnXX0kGSnDPKCZzQ"}], "time": "2021-01-16T03:39:31.350Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for wings paul mccartney", "titleUrl": "https://www.youtube.com/results?search_query=wings+paul+mccartney", "time": "2021-01-16T03:39:04.127Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "<PERSON><PERSON> <PERSON> - Blackbird (Live)", "titleUrl": "https://www.youtube.com/watch?v=RDxfjUEBT9I", "subtitles": [{"name": "PaulMcCartneyVids", "url": "https://www.youtube.com/channel/UCM-qvuPdqzHtDLWYIymb56A"}], "time": "2021-01-16T03:38:45.403Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> | Difference between Liberals and Conservatives", "titleUrl": "https://www.youtube.com/watch?v=8E6W9qJhtu8", "subtitles": [{"name": "School of Peterson", "url": "https://www.youtube.com/channel/UCK9U1l9Vou1rH46NZ77hKNA"}], "time": "2021-01-16T01:39:31.664Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - Gun Control", "titleUrl": "https://www.youtube.com/watch?v=Db0Y4qIZ4PA", "subtitles": [{"name": "bassanio1989", "url": "https://www.youtube.com/channel/UCJuGijzaZ-bpl14Jly694MQ"}], "time": "2021-01-16T00:36:43.998Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> -- Bullet Control (HD)", "titleUrl": "https://www.youtube.com/watch?v=VZrFVtmRXrw", "subtitles": [{"name": "HDStandUp", "url": "https://www.youtube.com/channel/UCpf3D0c6sKvo_WGUz8yiwGQ"}], "time": "2021-01-16T00:35:09.238Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - Repressed Citizen", "titleUrl": "https://www.youtube.com/watch?v=ZtYU87QNjPw", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UC1im-QPm5kkVlpdykVGLj3g"}], "time": "2021-01-15T12:15:52.231Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - It's My Life (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=vx2u5uUu3DE", "subtitles": [{"name": "BonJoviVEVO", "url": "https://www.youtube.com/channel/UCwlTofOAY79PS_GhmborAdA"}], "time": "2021-01-15T03:43:21.620Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for bon jovi it's my life ", "titleUrl": "https://www.youtube.com/results?search_query=bon+jovi+it%27s+my+life+", "time": "2021-01-15T03:43:08.791Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Creed - What's This Life For (Official Video)", "titleUrl": "https://www.youtube.com/watch?v=8lKBro5YkPs", "subtitles": [{"name": "CreedVEVO", "url": "https://www.youtube.com/channel/UCis-JwIzHYm1mKnNyPz9RJg"}], "time": "2021-01-15T03:37:08.332Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON> - Only Girl (In The World) (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=pa14VNsdSYM", "subtitles": [{"name": "RihannaVEVO", "url": "https://www.youtube.com/channel/UC2xskkQVFEpLcGFnNSLQY0A"}], "time": "2021-01-15T03:08:37.773Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON> - Where Have You Been", "titleUrl": "https://www.youtube.com/watch?v=HBxt_v0WF6Y", "subtitles": [{"name": "RihannaVEVO", "url": "https://www.youtube.com/channel/UC2xskkQVFEpLcGFnNSLQY0A"}], "time": "2021-01-15T03:06:25.501Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched A Nightmare On Elm Street 1984 - <PERSON>", "titleUrl": "https://www.youtube.com/watch?v=e-9RtbMyNTQ", "subtitles": [{"name": "el toro", "url": "https://www.youtube.com/channel/UC-riE2XugJetTr_aH-8rrpw"}], "time": "2021-01-15T03:06:01.096Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> - Best Show Ever!", "titleUrl": "https://www.youtube.com/watch?v=Qqaq7MIcN0A", "subtitles": [{"name": "O C", "url": "https://www.youtube.com/channel/UCbv4O0F4EF7DIZFHeelaC4Q"}], "time": "2021-01-15T03:05:53.077Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Google Security in Real Life | FULL SKIT Ft. Hardstop Lucas", "titleUrl": "https://www.youtube.com/watch?v=pBcPOx8_YKw", "subtitles": [{"name": "Ameratron", "url": "https://www.youtube.com/channel/UCYq6swvk29YnDmOPzVLs2XQ"}], "time": "2021-01-15T02:54:54.991Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Neo Uploads Knowledge Into His Head In The Matrix Movie", "titleUrl": "https://www.youtube.com/watch?v=w_8NsPQBdV0", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCn_oPH-Q12nhefCrmthDL6w"}], "time": "2021-01-15T02:17:19.081Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched GM’s CES 2021 keynote in 10 minutes", "titleUrl": "https://www.youtube.com/watch?v=lDFZqa422nI", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UC-6OW5aJYBFM33zXQlBKPNA"}], "time": "2021-01-14T17:24:30.842Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for tracking like people following your kids", "titleUrl": "https://www.youtube.com/results?search_query=tracking+like+people+following+your+kids", "time": "2021-01-14T16:43:23.192Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for cookies are like people following your kids", "titleUrl": "https://www.youtube.com/results?search_query=cookies+are+like+people+following+your+kids", "time": "2021-01-14T16:43:15.902Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for cookies are like people following you around", "titleUrl": "https://www.youtube.com/results?search_query=cookies+are+like+people+following+you+around", "time": "2021-01-14T16:42:50.063Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched How to Build a Mobile App Using AWS", "titleUrl": "https://www.youtube.com/watch?v=p7arftIf6U4", "subtitles": [{"name": "Amazon Web Services", "url": "https://www.youtube.com/channel/UCd6MoB9NC6uYN2grvUNT-Zg"}], "time": "2021-01-13T04:55:05.108Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Twisted Sister - We're Not Gonna Take it (Extended Version) (Official Music Video)", "titleUrl": "https://www.youtube.com/watch?v=V9AbeALNVkk", "subtitles": [{"name": "Twisted Sister", "url": "https://www.youtube.com/channel/UCRay0Axn67v8-nm6n91uBsg"}], "time": "2021-01-13T02:12:55.998Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched This is the OnePlus Band | #SmartEveryWear", "titleUrl": "https://www.youtube.com/watch?v=DYiOn9wBDs4", "subtitles": [{"name": "OnePlus India", "url": "https://www.youtube.com/channel/UCZDZuCogQfb1N95KuUOacjw"}], "time": "2021-01-12T01:10:16.211Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Why Tesla Stock Keeps Going Up (& can it go EVEN HIGHER?)", "titleUrl": "https://www.youtube.com/watch?v=WkfeL3JqBNA", "subtitles": [{"name": "Solving The Money Problem", "url": "https://www.youtube.com/channel/UCagiBBx1prefrlsDzDxuA9A"}], "time": "2021-01-11T11:43:51.231Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Aubrey Plaza has NO FILTER", "titleUrl": "https://www.youtube.com/watch?v=NYbf-1jsZqM", "subtitles": [{"name": "gretchentwo", "url": "https://www.youtube.com/channel/UCZi6Aa-bjrr5WYIwduCirCg"}], "time": "2021-01-11T11:35:19.379Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON> Interview - No One Will Replace <PERSON>", "titleUrl": "https://www.youtube.com/watch?v=cUOZlyqk2LM", "subtitles": [{"name": "DB Business", "url": "https://www.youtube.com/channel/UC1xLA4Ydt0csftFL5Z31drA"}], "time": "2021-01-11T11:32:55.475Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Things Only Adults Notice In Soul", "titleUrl": "https://www.youtube.com/watch?v=ksK5bpFZjqc", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCP1iRaFlS5EYjJBryFV9JPw"}], "time": "2021-01-11T00:27:54.325Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Disney and Pixar’s Soul | Official Trailer | Disney+", "titleUrl": "https://www.youtube.com/watch?v=xOsLIiBStEs", "subtitles": [{"name": "Pixar", "url": "https://www.youtube.com/channel/UC_IRYSp4auq7hKLvziWVH6w"}], "time": "2021-01-11T00:25:25.224Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON>: Capital makes you powerful", "titleUrl": "https://www.youtube.com/watch?v=yOy_MXoQVdE", "subtitles": [{"name": "Invest in Yourself", "url": "https://www.youtube.com/channel/UClRLSxD_S5EwpoGVFrvtjpw"}], "time": "2021-01-10T19:22:15.047Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 2021 Tesla Model S in-depth review – has it had its day? | What Car?", "titleUrl": "https://www.youtube.com/watch?v=A5PIgmXGIdI", "subtitles": [{"name": "What Car?", "url": "https://www.youtube.com/channel/UC-GJbheknHZhSM7-Jgn63jg"}], "time": "2021-01-08T20:20:41.514Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Subscribed to CNBC Television", "titleUrl": "https://www.youtube.com/channel/UCrp_UI8XtuYfpiqluWLD7Lw", "time": "2021-01-08T06:14:57.544Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON><PERSON><PERSON>: If I was at FB today, I would wonder what changed from 6 months ago", "titleUrl": "https://www.youtube.com/watch?v=gyBg6L5TIAw", "subtitles": [{"name": "CNBC Television", "url": "https://www.youtube.com/channel/UCrp_UI8XtuYfpiqluWLD7Lw"}], "time": "2021-01-08T06:10:30.230Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON> almost crying", "titleUrl": "https://www.youtube.com/watch?v=8P8UKBAOfGo", "subtitles": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UClKWrAO4-gFa-t4PzGB96hA"}], "time": "2021-01-07T14:46:54.601Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Just How Crazy is <PERSON><PERSON>'s Current Valuation?", "titleUrl": "https://www.youtube.com/watch?v=Njz8paSx-7Y", "subtitles": [{"name": "Good to Know", "url": "https://www.youtube.com/channel/UC1c8sy9YyzqoqY3XdvF6w3g"}], "time": "2021-01-07T13:41:59.990Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON>: Capital makes you powerful", "titleUrl": "https://www.youtube.com/watch?v=yOy_MXoQVdE", "subtitles": [{"name": "Invest in Yourself", "url": "https://www.youtube.com/channel/UClRLSxD_S5EwpoGVFrvtjpw"}], "time": "2021-01-07T05:29:53.508Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Wolf of Wall Street Official Trailer", "titleUrl": "https://www.youtube.com/watch?v=iszwuX1AK6A", "subtitles": [{"name": "Paramount Pictures", "url": "https://www.youtube.com/channel/UCF9imwPMSGz4Vq1NiTWCC7g"}], "time": "2021-01-07T00:24:25.362Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Just How Crazy is <PERSON><PERSON>'s Current Valuation?", "titleUrl": "https://www.youtube.com/watch?v=Njz8paSx-7Y", "subtitles": [{"name": "Good to Know", "url": "https://www.youtube.com/channel/UC1c8sy9YyzqoqY3XdvF6w3g"}], "time": "2021-01-06T16:20:21.769Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched GigaTexas - 1-3-2021 - A Quick Flight shows the progress at Texas Gigafactory is Stunning!", "titleUrl": "https://www.youtube.com/watch?v=G1I34Vg6gYM", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCsqD1BoHLri08rx72Nwgh_A"}], "time": "2021-01-05T21:55:29.749Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched How to Flirt", "titleUrl": "https://www.youtube.com/watch?v=A7yaX9-GNeU", "subtitles": [{"name": "Howcast", "url": "https://www.youtube.com/channel/UCSpVHeDGr9UbREhRca0qwsA"}], "time": "2021-01-04T14:43:42.816Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Code 46 | Official UK Trailer", "titleUrl": "https://www.youtube.com/watch?v=g7WgrrefZoc", "subtitles": [{"name": "Verve Pictures", "url": "https://www.youtube.com/channel/UCT6p6Ok23SSqIQLrRzDZLJQ"}], "time": "2021-01-04T04:48:57.243Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Honest Trailers | Tenet", "titleUrl": "https://www.youtube.com/watch?v=XkO-Wp8HrB0", "subtitles": [{"name": "Screen Junkies", "url": "https://www.youtube.com/channel/UCOpcACMWblDls9Z6GERVi1A"}], "time": "2021-01-04T01:10:44.209Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Idiocracy - Trailer", "titleUrl": "https://www.youtube.com/watch?v=BBvIweCIgwk", "subtitles": [{"name": "<PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCY9RSr8xj8WH6KIaxOTiCqA"}], "time": "2021-01-03T20:55:17.023Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for idiocracy trailer", "titleUrl": "https://www.youtube.com/results?search_query=idiocracy+trailer", "time": "2021-01-03T20:55:12.780Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Searched for idiocracy", "titleUrl": "https://www.youtube.com/results?search_query=idiocracy", "time": "2021-01-03T20:55:01.891Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Spaceballs (4/11) Movie CLIP - Ludicrous Speed (1987) HD", "titleUrl": "https://www.youtube.com/watch?v=NAWL8ejf2nM", "subtitles": [{"name": "Movieclips", "url": "https://www.youtube.com/channel/UC3gNmTGu-TTbFPpfSs5kNkg"}], "time": "2021-01-03T17:53:32.755Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Creating a Donation Widget in Aplos", "titleUrl": "https://www.youtube.com/watch?v=-SpUCbtCIF0", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2021-01-03T04:15:13.663Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Camp Lake James Vacation Rentals", "titleUrl": "https://www.youtube.com/watch?v=2MHvwoNtm2c", "subtitles": [{"name": "Camp Lake James", "url": "https://www.youtube.com/channel/UCDmPO7JKqYywI1umlDXVXbQ"}], "time": "2021-01-02T19:35:44.315Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched FIRST 2021 TESLA MODEL S REFRESH/PLAID SPY SHOTS! Chrome Delete, Wide Body, Rear Diffuser, & Wheels!", "titleUrl": "https://www.youtube.com/watch?v=Ugi7YoMF3bk", "subtitles": [{"name": "The Kilowatts", "url": "https://www.youtube.com/channel/UCD_QsRZod1gf2Ghm70_8IUA"}], "time": "2021-01-02T16:11:27.022Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Should 2020 Be Forgotten? A Birmingham Choir Rings Out The Year With “Auld Lang <PERSON>” | NPR", "titleUrl": "https://www.youtube.com/watch?v=dCFdKpKLGxs", "subtitles": [{"name": "NPR", "url": "https://www.youtube.com/channel/UCJnS2EsPfv46u1JR8cnD0NA"}], "time": "2021-01-01T02:03:16.720Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched What Self-Care Means to US! -Part 1", "titleUrl": "https://www.youtube.com/watch?v=N0omVkBtbEc", "subtitles": [{"name": "KeltyMentalHealth", "url": "https://www.youtube.com/channel/UCGHwOGU4HVQi4ImFfYsItJg"}], "time": "2020-12-31T20:52:10.405Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Big Bang Theory - The Internet", "titleUrl": "https://www.youtube.com/watch?v=X5_MAxoYwsQ", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC-UDGfuM-5bTT-CJZ_MQjSQ"}], "time": "2020-12-31T03:30:32.451Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Starship | SN8 | High-Altitude Flight Recap", "titleUrl": "https://www.youtube.com/watch?v=_qwLHlVjRyw", "subtitles": [{"name": "SpaceX", "url": "https://www.youtube.com/channel/UCtI0Hodo5o5dUb67FeUjDeA"}], "time": "2020-12-30T22:20:23.594Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched RGF® Environmental Group Study: REME HALO Inactivates SARS-CoV-2 by 99.9%", "titleUrl": "https://www.youtube.com/watch?v=0C-i3u3jHGE", "subtitles": [{"name": "RGF® Environmental Group, Inc.", "url": "https://www.youtube.com/channel/UCsAYAZ4Bgdpt1dhnjqj9jUQ"}], "time": "2020-12-30T18:14:31.831Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched THIS IS HUGE!!! New Tesla Model S And Model X Coming Soon", "titleUrl": "https://www.youtube.com/watch?v=AGn0h8kShtc", "subtitles": [{"name": "Infowealth", "url": "https://www.youtube.com/channel/UCs8eGmm3In4xR99Nopn1Vfw"}], "time": "2020-12-30T15:03:04.963Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Ford Mustang Mach-E vs. Tesla Model Y: Did Ford Build A BETTER Tesla?", "titleUrl": "https://www.youtube.com/watch?v=rUjDBYQkhwM", "subtitles": [{"name": "The Fast Lane Car", "url": "https://www.youtube.com/channel/UC6S0jAvcapqJ48ZzLfva12g"}], "time": "2020-12-30T13:43:27.893Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla Model X review door <PERSON> | The Grand Tour | Amazon Prime Video NL", "titleUrl": "https://www.youtube.com/watch?v=pXvIzWhEWvA", "subtitles": [{"name": "Amazon Prime Video Nederland", "url": "https://www.youtube.com/channel/UCQcgrnC4tC5EPuXbaA_hSiA"}], "time": "2020-12-30T13:43:02.778Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The 2021 Ford Mustang Mach-E Is the Fast, Electric Mustang SUV", "titleUrl": "https://www.youtube.com/watch?v=c4n5iPqxpaw", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UCsqjHFMB_JYTaEnf_vmTNqg"}], "time": "2020-12-30T13:37:05.044Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Fundamentals of Nonprofit Budgeting", "titleUrl": "https://www.youtube.com/watch?v=VFsIAudxrlc", "subtitles": [{"name": "Propel Nonprofits", "url": "https://www.youtube.com/channel/UCVYBtJodgJtN7j2Pb-BMqLg"}], "time": "2020-12-30T04:21:51.283Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla Breaking News - FSD FREE FOR 3 MONTHS!!!", "titleUrl": "https://www.youtube.com/watch?v=NLnW8Cvyff0", "subtitles": [{"name": "Bearded <PERSON><PERSON>", "url": "https://www.youtube.com/channel/UCBsANJdoKs-BVz-kLT2BeVQ"}], "time": "2020-12-30T02:51:29.504Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Ford Plantation - \"Uncommon Living\"", "titleUrl": "https://www.youtube.com/watch?v=wDoVeMF-qFI", "subtitles": [{"name": "FordPlantation", "url": "https://www.youtube.com/channel/UCkVBf2KrLKpmBiFF-4rmeag"}], "time": "2020-12-30T00:43:04.889Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Do You Love Me?", "titleUrl": "https://www.youtube.com/watch?v=fn3KWM1kuAw", "subtitles": [{"name": "Boston Dynamics", "url": "https://www.youtube.com/channel/UC7vVhkEfw4nOGp8TyDk7RcQ"}], "time": "2020-12-29T20:39:06.552Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Crazy Eyes", "titleUrl": "https://www.youtube.com/watch?v=kCGFoA26y50", "subtitles": [{"name": "German Cardenas", "url": "https://www.youtube.com/channel/UCLn9i_k0v7TscIbeFYQRStQ"}], "time": "2020-12-29T01:40:17.106Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Goldmine: Creating A Winning Recurring Donor Program", "titleUrl": "https://www.youtube.com/watch?v=FepctmsLIjw", "subtitles": [{"name": "Neon One", "url": "https://www.youtube.com/channel/UCJTT5eSnssJbHiYc6Lk7iZA"}], "time": "2020-12-28T19:19:54.330Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched The Data Driven Nonprofit:  Why it Matters and How You Get There", "titleUrl": "https://www.youtube.com/watch?v=rxpwYFugvIA", "subtitles": [{"name": "Neon One", "url": "https://www.youtube.com/channel/UCJTT5eSnssJbHiYc6Lk7iZA"}], "time": "2020-12-28T18:30:17.114Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON>ris - Move Bitch Get Out Da Way (HQ)", "titleUrl": "https://www.youtube.com/watch?v=cEuU64Zt4B0", "subtitles": [{"name": "ZLife", "url": "https://www.youtube.com/channel/UCJIkOWKlPVdVrG4Glk31YwQ"}], "time": "2020-12-28T17:16:50.007Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 🎄 FIRST LOOK! Tesla Software 2020.48.26 aka: \"Holiday update\"", "titleUrl": "https://www.youtube.com/watch?v=P6WeD7L2s0E", "subtitles": [{"name": "Tesla Owners Online", "url": "https://www.youtube.com/channel/UCry4jW5bcj9DIs7ZwA95Ylw"}], "time": "2020-12-28T17:15:44.562Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Pickleball Rule Change! Great Drop Serves to add to your game!", "titleUrl": "https://www.youtube.com/watch?v=dPYTI6Iy08k", "subtitles": [{"name": "PickleballTV", "url": "https://www.youtube.com/channel/UC4BMmHhcF_AFzUm1eY0XCxw"}], "time": "2020-12-28T16:57:15.595Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 2017 Tesla Model X - Christmas Dance", "titleUrl": "https://www.youtube.com/watch?v=u-i_R5prdMM", "subtitles": [{"name": "Romans International", "url": "https://www.youtube.com/channel/UCBg2gJ1pqzGvRQPN-cLim_g"}], "time": "2020-12-28T04:24:09.719Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON>: There’s more to investing than making money | <PERSON> | Yang Speaks", "titleUrl": "https://www.youtube.com/watch?v=AHcsgy5gk58", "subtitles": [{"name": "<PERSON>s", "url": "https://www.youtube.com/channel/UCJjLcmTHbVigXBb1ul0m5sw"}], "time": "2020-12-28T02:05:16.225Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched There's a Horse In The Hospital | <PERSON> | Netflix Is A Joke", "titleUrl": "https://www.youtube.com/watch?v=JhkZMxgPxXU", "subtitles": [{"name": "Netflix Is A Joke", "url": "https://www.youtube.com/channel/UCObk_g1hQBy0RKKriVX_zOQ"}], "time": "2020-12-27T23:20:27.507Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Aplos Support: Entering and Paying Bills", "titleUrl": "https://www.youtube.com/watch?v=QTjExz1812Q", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-27T05:11:00.902Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Aplos Support: Track Donations and Prepare Your Bank Deposits", "titleUrl": "https://www.youtube.com/watch?v=rSZKTLqDYpU", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-27T05:03:55.040Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched NEWS OF THE WORLD Trailer (2020) Tom <PERSON> Movie", "titleUrl": "https://www.youtube.com/watch?v=GwCmvi4GzII", "subtitles": [{"name": "JoBlo Movie Trailers", "url": "https://www.youtube.com/channel/UCRX7UEyE8kp35mPrgC2sosA"}], "time": "2020-12-27T02:58:52.430Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> in Their Flying Machines", "titleUrl": "https://www.youtube.com/watch?v=_kwyuy5neto", "subtitles": [{"name": "LoredoSeixas", "url": "https://www.youtube.com/channel/UC7GtSDKx7CXXLkqC9g5F0sA"}], "time": "2020-12-27T02:51:38.173Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Das<PERSON><PERSON> and <PERSON><PERSON><PERSON> | <PERSON> | Boomerang Official", "titleUrl": "https://www.youtube.com/watch?v=c8Au0VLo1jI", "subtitles": [{"name": "Boomerang Official", "url": "https://www.youtube.com/channel/UCx8nU9xWEyden60hkKBaAvg"}], "time": "2020-12-27T02:50:00.563Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Aplos Support: Enter Income and Expenses", "titleUrl": "https://www.youtube.com/watch?v=bNwklub9phY", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-27T01:20:26.374Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched What is a Nonprofit Chart of Accounts? [Easily Explained]", "titleUrl": "https://www.youtube.com/watch?v=NiaXMX-EZSA", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-26T21:06:35.939Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Bookkeeping Basics in Aplos", "titleUrl": "https://www.youtube.com/watch?v=TOfzUXSheG8", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-26T15:51:33.617Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Solar Off-Grid Tiny House", "titleUrl": "https://www.youtube.com/watch?v=dI_KsWUExiI", "subtitles": [{"name": "GoSun", "url": "https://www.youtube.com/channel/UCk5nTdd5QoHlMTIpapecxjA"}], "time": "2020-12-26T13:18:05.361Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Austin Power: International Man of Mystery - Original Theatrical Trailer", "titleUrl": "https://www.youtube.com/watch?v=JCSMpRHcNuM", "subtitles": [{"name": "Warner Bros.", "url": "https://www.youtube.com/channel/UCsQQo8qb62ikp9kc954V7eQ"}], "time": "2020-12-26T00:32:31.215Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched <PERSON> vs Bad Packaging", "titleUrl": "https://www.youtube.com/watch?v=HubZInAs0-A", "subtitles": [{"name": "<PERSON>", "url": "https://www.youtube.com/channel/UC1wC-PG5G6HrbhuH6z4YgIg"}], "time": "2020-12-25T21:11:24.688Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Honest Trailers | Tenet", "titleUrl": "https://www.youtube.com/watch?v=XkO-Wp8HrB0", "subtitles": [{"name": "Screen Junkies", "url": "https://www.youtube.com/channel/UCOpcACMWblDls9Z6GERVi1A"}], "time": "2020-12-25T20:19:30.411Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Storybook for frontend development", "titleUrl": "https://www.youtube.com/watch?v=p-LFh5Y89eM", "subtitles": [{"name": "Storybook", "url": "https://www.youtube.com/channel/UCr7Quur3eIyA_oe8FNYexfg"}], "time": "2020-12-25T04:29:16.332Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Bookkeeping Basics in Aplos", "titleUrl": "https://www.youtube.com/watch?v=TOfzUXSheG8", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-24T22:27:44.877Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Setting Up Your Accounting", "titleUrl": "https://www.youtube.com/watch?v=oPy6rG7_hZw", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-24T18:05:07.903Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Fundamentals of Accounts, Funds, and Tags", "titleUrl": "https://www.youtube.com/watch?v=qXA9H8Fp7QY", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-24T14:16:12.199Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Tesla Model S and X Refresh May Be Imminent On January 4th with 4680 Cells", "titleUrl": "https://www.youtube.com/watch?v=hRton68x3cY", "subtitles": [{"name": "Torque News", "url": "https://www.youtube.com/channel/UCe9o5o8JnpzLH0jWQ1r4opw"}], "time": "2020-12-24T05:15:06.671Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Fundamentals of Accounts, Funds, and Tags", "titleUrl": "https://www.youtube.com/watch?v=qXA9H8Fp7QY", "subtitles": [{"name": "Aplos", "url": "https://www.youtube.com/channel/UCmWC-HpbvkAb5EFvDGocfoA"}], "time": "2020-12-23T18:18:53.235Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched I Want My Mtv", "titleUrl": "https://www.youtube.com/watch?v=AGZSWdh17l0", "subtitles": [{"name": "guru<PERSON>ess", "url": "https://www.youtube.com/channel/UC-S7hZcLk6l_G4XF_YbYFHA"}], "time": "2020-12-23T14:06:07.187Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched What's So Bad About Feeling Good? (1968) Radio Spot", "titleUrl": "https://www.youtube.com/watch?v=_c3Mbr89ncI", "subtitles": [{"name": "Night Of The Trailers", "url": "https://www.youtube.com/channel/UCufR4LKZX05hHVwh5ZluikA"}], "time": "2020-12-22T09:32:55.875Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched 0.000578% Chance Among Us Impostor - Among Us #11", "titleUrl": "https://www.youtube.com/watch?v=NWDBbJUBpzI", "subtitles": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://www.youtube.com/channel/UC-lHJZR3Gqxm24_Vd_AJ5Yw"}], "time": "2020-12-21T12:59:17.146Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched What is <PERSON><PERSON><PERSON>?", "titleUrl": "https://www.youtube.com/watch?v=Qtwt7QcW4J8", "subtitles": [{"name": "Google Cloud Tech", "url": "https://www.youtube.com/channel/UCJS9pqu9BzkAMNTmzNMNhvg"}], "time": "2020-12-20T21:44:09.828Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Dr<PERSON> & Dr. Birx Cold Open - SNL", "titleUrl": "https://www.youtube.com/watch?v=IqgyBN0OIns", "subtitles": [{"name": "Saturday Night Live", "url": "https://www.youtube.com/channel/UCqFzWxSCi39LnW1JKFR3efg"}], "time": "2020-12-20T02:34:50.702Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Rumpke Mountain Boys - Low me", "titleUrl": "https://www.youtube.com/watch?v=FFteJWkJ6sM", "subtitles": [{"name": "wcbe columbus", "url": "https://www.youtube.com/channel/UCwi7_Y_956xv-dpfVg4KP_g"}], "time": "2020-12-20T01:35:02.035Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Rumpke Mountain Boys", "titleUrl": "https://www.youtube.com/watch?v=78sEKi34QsA", "subtitles": [{"name": "Rumpke Mountain Boys", "url": "https://www.youtube.com/channel/UC55EvVDRD0HGOEh-ky5vAkQ"}], "time": "2020-12-20T01:32:41.595Z", "products": ["YouTube"]}, {"header": "YouTube", "title": "Watched Rumpke Mountain Boys (Night 2) @ Pisgah Brewing Co. 12-16-2017", "titleUrl": "https://www.youtube.com/watch?v=5RxnTG5ZteE", "subtitles": [{"name": "Iam AVL", "url": "https://www.youtube.com/channel/UC8tiqTOdPqRnO11OnYsfe1w"}], "time": "2020-12-20T01:32:36.765Z", "products": ["YouTube"]}]