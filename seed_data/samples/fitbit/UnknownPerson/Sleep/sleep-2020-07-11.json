[{"logId": 28394477665, "dateOfSleep": "2020-08-10", "startTime": "2020-08-10T01:07:00.000", "endTime": "2020-08-10T06:43:30.000", "duration": 20160000, "minutesToFallAsleep": 0, "minutesAsleep": 301, "minutesAwake": 35, "minutesAfterWakeup": 0, "timeInBed": 336, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 45, "thirtyDayAvgMinutes": 40}, "wake": {"count": 16, "minutes": 35, "thirtyDayAvgMinutes": 51}, "light": {"count": 17, "minutes": 184, "thirtyDayAvgMinutes": 231}, "rem": {"count": 4, "minutes": 72, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-08-10T01:07:00.000", "level": "wake", "seconds": 720}, {"dateTime": "2020-08-10T01:19:00.000", "level": "light", "seconds": 1560}, {"dateTime": "2020-08-10T01:45:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-10T01:50:00.000", "level": "light", "seconds": 1710}, {"dateTime": "2020-08-10T02:18:30.000", "level": "rem", "seconds": 2280}, {"dateTime": "2020-08-10T02:56:30.000", "level": "light", "seconds": 4200}, {"dateTime": "2020-08-10T04:06:30.000", "level": "rem", "seconds": 1740}, {"dateTime": "2020-08-10T04:35:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-10T04:41:00.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-10T04:42:30.000", "level": "deep", "seconds": 2370}, {"dateTime": "2020-08-10T05:22:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-10T05:29:00.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-08-10T05:35:00.000", "level": "light", "seconds": 1950}, {"dateTime": "2020-08-10T06:07:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-10T06:13:30.000", "level": "light", "seconds": 1800}], "shortData": [{"dateTime": "2020-08-10T01:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:53:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:37:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T03:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T05:21:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:23:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:43:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T05:51:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T06:18:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:24:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:41:00.000", "level": "wake", "seconds": 150}]}, "mainSleep": true}, {"logId": 28382679124, "dateOfSleep": "2020-08-09", "startTime": "2020-08-09T01:56:00.000", "endTime": "2020-08-09T08:03:00.000", "duration": 22020000, "minutesToFallAsleep": 0, "minutesAsleep": 326, "minutesAwake": 41, "minutesAfterWakeup": 0, "timeInBed": 367, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 48, "thirtyDayAvgMinutes": 40}, "wake": {"count": 19, "minutes": 41, "thirtyDayAvgMinutes": 51}, "light": {"count": 20, "minutes": 226, "thirtyDayAvgMinutes": 231}, "rem": {"count": 3, "minutes": 52, "thirtyDayAvgMinutes": 61}}, "data": [{"dateTime": "2020-08-09T01:56:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T01:56:30.000", "level": "light", "seconds": 6300}, {"dateTime": "2020-08-09T03:41:30.000", "level": "deep", "seconds": 420}, {"dateTime": "2020-08-09T03:48:30.000", "level": "rem", "seconds": 1500}, {"dateTime": "2020-08-09T04:13:30.000", "level": "light", "seconds": 3210}, {"dateTime": "2020-08-09T05:07:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-09T05:11:30.000", "level": "light", "seconds": 240}, {"dateTime": "2020-08-09T05:15:30.000", "level": "wake", "seconds": 420}, {"dateTime": "2020-08-09T05:22:30.000", "level": "light", "seconds": 2100}, {"dateTime": "2020-08-09T05:57:30.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-09T06:01:30.000", "level": "light", "seconds": 600}, {"dateTime": "2020-08-09T06:11:30.000", "level": "rem", "seconds": 1350}, {"dateTime": "2020-08-09T06:34:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-09T06:39:00.000", "level": "light", "seconds": 1500}, {"dateTime": "2020-08-09T07:04:00.000", "level": "deep", "seconds": 2520}, {"dateTime": "2020-08-09T07:46:00.000", "level": "light", "seconds": 630}, {"dateTime": "2020-08-09T07:56:30.000", "level": "wake", "seconds": 390}], "shortData": [{"dateTime": "2020-08-09T02:46:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T03:10:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T03:18:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-09T03:21:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-09T03:26:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T04:23:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T04:25:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T05:00:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-09T05:04:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T05:33:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-09T06:51:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T06:56:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-09T06:59:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-09T07:45:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28370288328, "dateOfSleep": "2020-08-08", "startTime": "2020-08-08T01:30:30.000", "endTime": "2020-08-08T07:19:30.000", "duration": 20940000, "minutesToFallAsleep": 0, "minutesAsleep": 313, "minutesAwake": 36, "minutesAfterWakeup": 0, "timeInBed": 349, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 3, "minutes": 46, "thirtyDayAvgMinutes": 40}, "wake": {"count": 22, "minutes": 36, "thirtyDayAvgMinutes": 51}, "light": {"count": 24, "minutes": 246, "thirtyDayAvgMinutes": 230}, "rem": {"count": 2, "minutes": 21, "thirtyDayAvgMinutes": 62}}, "data": [{"dateTime": "2020-08-08T01:30:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T01:31:00.000", "level": "light", "seconds": 240}, {"dateTime": "2020-08-08T01:35:00.000", "level": "deep", "seconds": 930}, {"dateTime": "2020-08-08T01:50:30.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-08T01:57:30.000", "level": "deep", "seconds": 1590}, {"dateTime": "2020-08-08T02:24:00.000", "level": "light", "seconds": 450}, {"dateTime": "2020-08-08T02:31:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-08T02:36:30.000", "level": "light", "seconds": 2820}, {"dateTime": "2020-08-08T03:23:30.000", "level": "wake", "seconds": 540}, {"dateTime": "2020-08-08T03:32:30.000", "level": "light", "seconds": 8190}, {"dateTime": "2020-08-08T05:49:00.000", "level": "rem", "seconds": 270}, {"dateTime": "2020-08-08T05:53:30.000", "level": "light", "seconds": 3210}, {"dateTime": "2020-08-08T06:47:00.000", "level": "rem", "seconds": 960}, {"dateTime": "2020-08-08T07:03:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-08T07:08:00.000", "level": "light", "seconds": 690}], "shortData": [{"dateTime": "2020-08-08T01:50:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T02:29:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T02:39:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T02:49:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T03:43:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-08T03:47:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-08T04:17:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T04:21:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T04:25:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-08T04:35:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T04:49:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T04:53:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T05:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-08T05:40:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T05:44:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T05:53:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-08T06:02:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T06:30:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-08T06:42:00.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28363184237, "dateOfSleep": "2020-08-07", "startTime": "2020-08-07T14:46:30.000", "endTime": "2020-08-07T15:46:30.000", "duration": 3600000, "minutesToFallAsleep": 0, "minutesAsleep": 52, "minutesAwake": 7, "minutesAfterWakeup": 1, "timeInBed": 60, "efficiency": 88, "type": "classic", "infoCode": 2, "levels": {"summary": {"restless": {"count": 4, "minutes": 7}, "awake": {"count": 1, "minutes": 1}, "asleep": {"count": 0, "minutes": 52}}, "data": [{"dateTime": "2020-08-07T14:46:30.000", "level": "asleep", "seconds": 180}, {"dateTime": "2020-08-07T14:49:30.000", "level": "restless", "seconds": 180}, {"dateTime": "2020-08-07T14:52:30.000", "level": "asleep", "seconds": 1680}, {"dateTime": "2020-08-07T15:20:30.000", "level": "restless", "seconds": 60}, {"dateTime": "2020-08-07T15:21:30.000", "level": "awake", "seconds": 60}, {"dateTime": "2020-08-07T15:22:30.000", "level": "asleep", "seconds": 1080}, {"dateTime": "2020-08-07T15:40:30.000", "level": "restless", "seconds": 120}, {"dateTime": "2020-08-07T15:42:30.000", "level": "asleep", "seconds": 180}, {"dateTime": "2020-08-07T15:45:30.000", "level": "restless", "seconds": 60}]}, "mainSleep": false}, {"logId": 28358361370, "dateOfSleep": "2020-08-07", "startTime": "2020-08-07T01:00:00.000", "endTime": "2020-08-07T07:25:30.000", "duration": 23100000, "minutesToFallAsleep": 0, "minutesAsleep": 340, "minutesAwake": 45, "minutesAfterWakeup": 7, "timeInBed": 385, "efficiency": 98, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 4, "minutes": 24, "thirtyDayAvgMinutes": 40}, "wake": {"count": 17, "minutes": 45, "thirtyDayAvgMinutes": 52}, "light": {"count": 17, "minutes": 256, "thirtyDayAvgMinutes": 229}, "rem": {"count": 4, "minutes": 60, "thirtyDayAvgMinutes": 62}}, "data": [{"dateTime": "2020-08-07T01:00:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-07T01:05:00.000", "level": "light", "seconds": 2340}, {"dateTime": "2020-08-07T01:44:00.000", "level": "deep", "seconds": 390}, {"dateTime": "2020-08-07T01:50:30.000", "level": "light", "seconds": 1200}, {"dateTime": "2020-08-07T02:10:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-07T02:15:30.000", "level": "light", "seconds": 810}, {"dateTime": "2020-08-07T02:29:00.000", "level": "rem", "seconds": 900}, {"dateTime": "2020-08-07T02:44:00.000", "level": "light", "seconds": 450}, {"dateTime": "2020-08-07T02:51:30.000", "level": "deep", "seconds": 450}, {"dateTime": "2020-08-07T02:59:00.000", "level": "light", "seconds": 1680}, {"dateTime": "2020-08-07T03:27:00.000", "level": "rem", "seconds": 1740}, {"dateTime": "2020-08-07T03:56:00.000", "level": "light", "seconds": 2310}, {"dateTime": "2020-08-07T04:34:30.000", "level": "rem", "seconds": 1140}, {"dateTime": "2020-08-07T04:53:30.000", "level": "light", "seconds": 3420}, {"dateTime": "2020-08-07T05:50:30.000", "level": "wake", "seconds": 600}, {"dateTime": "2020-08-07T06:00:30.000", "level": "light", "seconds": 900}, {"dateTime": "2020-08-07T06:15:30.000", "level": "deep", "seconds": 300}, {"dateTime": "2020-08-07T06:20:30.000", "level": "light", "seconds": 2370}, {"dateTime": "2020-08-07T07:00:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-08-07T07:06:00.000", "level": "light", "seconds": 720}, {"dateTime": "2020-08-07T07:18:00.000", "level": "wake", "seconds": 450}], "shortData": [{"dateTime": "2020-08-07T01:50:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-07T02:01:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-07T02:58:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T03:01:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T03:44:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T04:05:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T04:14:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T04:53:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T05:01:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-07T05:06:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-07T05:29:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-07T06:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-07T06:20:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}]