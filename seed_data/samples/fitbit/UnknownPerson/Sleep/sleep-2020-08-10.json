[{"logId": 28438342723, "dateOfSleep": "2020-08-13", "startTime": "2020-08-13T02:29:00.000", "endTime": "2020-08-13T06:50:30.000", "duration": 15660000, "minutesToFallAsleep": 0, "minutesAsleep": 226, "minutesAwake": 35, "minutesAfterWakeup": 0, "timeInBed": 261, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 28, "thirtyDayAvgMinutes": 45}, "wake": {"count": 18, "minutes": 35, "thirtyDayAvgMinutes": 56}, "light": {"count": 19, "minutes": 157, "thirtyDayAvgMinutes": 220}, "rem": {"count": 4, "minutes": 41, "thirtyDayAvgMinutes": 60}}, "data": [{"dateTime": "2020-08-13T02:29:00.000", "level": "light", "seconds": 180}, {"dateTime": "2020-08-13T02:32:00.000", "level": "deep", "seconds": 1800}, {"dateTime": "2020-08-13T03:02:00.000", "level": "light", "seconds": 4110}, {"dateTime": "2020-08-13T04:10:30.000", "level": "rem", "seconds": 1650}, {"dateTime": "2020-08-13T04:38:00.000", "level": "light", "seconds": 4500}, {"dateTime": "2020-08-13T05:53:00.000", "level": "wake", "seconds": 570}, {"dateTime": "2020-08-13T06:02:30.000", "level": "light", "seconds": 570}, {"dateTime": "2020-08-13T06:12:00.000", "level": "rem", "seconds": 660}, {"dateTime": "2020-08-13T06:23:00.000", "level": "light", "seconds": 510}, {"dateTime": "2020-08-13T06:31:30.000", "level": "rem", "seconds": 330}, {"dateTime": "2020-08-13T06:37:00.000", "level": "light", "seconds": 210}, {"dateTime": "2020-08-13T06:40:30.000", "level": "wake", "seconds": 600}], "shortData": [{"dateTime": "2020-08-13T03:00:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-13T03:04:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-13T03:11:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T03:37:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T03:39:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T03:47:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T03:58:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T04:21:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T04:46:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T05:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T05:26:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T05:35:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-13T05:39:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-13T05:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-13T06:20:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-13T06:26:00.000", "level": "wake", "seconds": 60}]}, "mainSleep": true}, {"logId": 28426574047, "dateOfSleep": "2020-08-12", "startTime": "2020-08-12T01:20:00.000", "endTime": "2020-08-12T07:30:30.000", "duration": 22200000, "minutesToFallAsleep": 0, "minutesAsleep": 313, "minutesAwake": 57, "minutesAfterWakeup": 4, "timeInBed": 370, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 1, "minutes": 35, "thirtyDayAvgMinutes": 50}, "wake": {"count": 20, "minutes": 57, "thirtyDayAvgMinutes": 56}, "light": {"count": 20, "minutes": 210, "thirtyDayAvgMinutes": 225}, "rem": {"count": 2, "minutes": 68, "thirtyDayAvgMinutes": 56}}, "data": [{"dateTime": "2020-08-12T01:20:00.000", "level": "wake", "seconds": 360}, {"dateTime": "2020-08-12T01:26:00.000", "level": "light", "seconds": 390}, {"dateTime": "2020-08-12T01:32:30.000", "level": "deep", "seconds": 2130}, {"dateTime": "2020-08-12T02:08:00.000", "level": "wake", "seconds": 210}, {"dateTime": "2020-08-12T02:11:30.000", "level": "light", "seconds": 2160}, {"dateTime": "2020-08-12T02:47:30.000", "level": "rem", "seconds": 2310}, {"dateTime": "2020-08-12T03:26:00.000", "level": "wake", "seconds": 1200}, {"dateTime": "2020-08-12T03:46:00.000", "level": "light", "seconds": 3630}, {"dateTime": "2020-08-12T04:46:30.000", "level": "rem", "seconds": 1770}, {"dateTime": "2020-08-12T05:16:00.000", "level": "light", "seconds": 7770}, {"dateTime": "2020-08-12T07:25:30.000", "level": "wake", "seconds": 300}], "shortData": [{"dateTime": "2020-08-12T02:16:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T02:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-12T04:09:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-12T04:13:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T04:16:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T04:30:30.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-12T04:34:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T05:19:30.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-12T05:48:00.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T06:13:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T06:16:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-12T06:35:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T06:38:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-12T06:56:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-12T07:09:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-12T07:15:00.000", "level": "wake", "seconds": 120}]}, "mainSleep": true}, {"logId": 28408189132, "dateOfSleep": "2020-08-11", "startTime": "2020-08-11T00:37:00.000", "endTime": "2020-08-11T07:53:00.000", "duration": 26160000, "minutesToFallAsleep": 0, "minutesAsleep": 359, "minutesAwake": 77, "minutesAfterWakeup": 0, "timeInBed": 436, "efficiency": 94, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 54, "thirtyDayAvgMinutes": 45}, "wake": {"count": 20, "minutes": 77, "thirtyDayAvgMinutes": 35}, "light": {"count": 21, "minutes": 265, "thirtyDayAvgMinutes": 184}, "rem": {"count": 4, "minutes": 40, "thirtyDayAvgMinutes": 72}}, "data": [{"dateTime": "2020-08-11T00:37:00.000", "level": "light", "seconds": 3000}, {"dateTime": "2020-08-11T01:27:00.000", "level": "deep", "seconds": 1110}, {"dateTime": "2020-08-11T01:45:30.000", "level": "light", "seconds": 1020}, {"dateTime": "2020-08-11T02:02:30.000", "level": "rem", "seconds": 570}, {"dateTime": "2020-08-11T02:12:00.000", "level": "light", "seconds": 1140}, {"dateTime": "2020-08-11T02:31:00.000", "level": "rem", "seconds": 570}, {"dateTime": "2020-08-11T02:40:30.000", "level": "light", "seconds": 2910}, {"dateTime": "2020-08-11T03:29:00.000", "level": "wake", "seconds": 810}, {"dateTime": "2020-08-11T03:42:30.000", "level": "light", "seconds": 120}, {"dateTime": "2020-08-11T03:44:30.000", "level": "wake", "seconds": 990}, {"dateTime": "2020-08-11T04:01:00.000", "level": "light", "seconds": 660}, {"dateTime": "2020-08-11T04:12:00.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-11T04:17:30.000", "level": "light", "seconds": 690}, {"dateTime": "2020-08-11T04:29:00.000", "level": "wake", "seconds": 1140}, {"dateTime": "2020-08-11T04:48:00.000", "level": "light", "seconds": 3120}, {"dateTime": "2020-08-11T05:40:00.000", "level": "rem", "seconds": 1440}, {"dateTime": "2020-08-11T06:04:00.000", "level": "wake", "seconds": 240}, {"dateTime": "2020-08-11T06:08:00.000", "level": "light", "seconds": 1170}, {"dateTime": "2020-08-11T06:27:30.000", "level": "deep", "seconds": 2190}, {"dateTime": "2020-08-11T07:04:00.000", "level": "light", "seconds": 2940}], "shortData": [{"dateTime": "2020-08-11T02:15:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T02:19:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T02:37:30.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-11T02:47:00.000", "level": "wake", "seconds": 150}, {"dateTime": "2020-08-11T03:04:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T04:24:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-11T05:47:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:03:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:07:00.000", "level": "wake", "seconds": 120}, {"dateTime": "2020-08-11T07:18:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:20:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-11T07:26:00.000", "level": "wake", "seconds": 180}, {"dateTime": "2020-08-11T07:30:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-11T07:39:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-11T07:52:30.000", "level": "wake", "seconds": 30}]}, "mainSleep": true}, {"logId": 28394477665, "dateOfSleep": "2020-08-10", "startTime": "2020-08-10T01:07:00.000", "endTime": "2020-08-10T06:43:30.000", "duration": 20160000, "minutesToFallAsleep": 0, "minutesAsleep": 301, "minutesAwake": 35, "minutesAfterWakeup": 0, "timeInBed": 336, "efficiency": 96, "type": "stages", "infoCode": 0, "levels": {"summary": {"deep": {"count": 2, "minutes": 45, "thirtyDayAvgMinutes": 0}, "wake": {"count": 16, "minutes": 35, "thirtyDayAvgMinutes": 0}, "light": {"count": 17, "minutes": 184, "thirtyDayAvgMinutes": 0}, "rem": {"count": 4, "minutes": 72, "thirtyDayAvgMinutes": 0}}, "data": [{"dateTime": "2020-08-10T01:07:00.000", "level": "wake", "seconds": 720}, {"dateTime": "2020-08-10T01:19:00.000", "level": "light", "seconds": 1560}, {"dateTime": "2020-08-10T01:45:00.000", "level": "wake", "seconds": 300}, {"dateTime": "2020-08-10T01:50:00.000", "level": "light", "seconds": 1710}, {"dateTime": "2020-08-10T02:18:30.000", "level": "rem", "seconds": 2280}, {"dateTime": "2020-08-10T02:56:30.000", "level": "light", "seconds": 4200}, {"dateTime": "2020-08-10T04:06:30.000", "level": "rem", "seconds": 1740}, {"dateTime": "2020-08-10T04:35:30.000", "level": "wake", "seconds": 330}, {"dateTime": "2020-08-10T04:41:00.000", "level": "light", "seconds": 90}, {"dateTime": "2020-08-10T04:42:30.000", "level": "deep", "seconds": 2370}, {"dateTime": "2020-08-10T05:22:00.000", "level": "light", "seconds": 420}, {"dateTime": "2020-08-10T05:29:00.000", "level": "rem", "seconds": 360}, {"dateTime": "2020-08-10T05:35:00.000", "level": "light", "seconds": 1950}, {"dateTime": "2020-08-10T06:07:30.000", "level": "deep", "seconds": 360}, {"dateTime": "2020-08-10T06:13:30.000", "level": "light", "seconds": 1800}], "shortData": [{"dateTime": "2020-08-10T01:59:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:05:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T02:53:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:28:00.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T03:37:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T03:45:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T05:21:00.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:23:30.000", "level": "wake", "seconds": 60}, {"dateTime": "2020-08-10T05:43:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T05:51:30.000", "level": "wake", "seconds": 30}, {"dateTime": "2020-08-10T06:18:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:24:30.000", "level": "wake", "seconds": 90}, {"dateTime": "2020-08-10T06:41:00.000", "level": "wake", "seconds": 150}]}, "mainSleep": true}]