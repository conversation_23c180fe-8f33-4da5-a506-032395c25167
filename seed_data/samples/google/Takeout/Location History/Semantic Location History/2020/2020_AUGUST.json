{"timelineObjects": [{"activitySegment": {"startLocation": {"latitudeE7": 359322595, "longitudeE7": -785680495, "sourceInfo": {"deviceTag": -1285813491}}, "endLocation": {"latitudeE7": *********, "longitudeE7": -*********, "sourceInfo": {"deviceTag": -1285813491}}, "duration": {"startTimestamp": "2020-08-02T15:53:10.889Z", "endTimestamp": "2020-08-02T16:10:28.500Z"}, "distance": 17493, "activityType": "IN_PASSENGER_VEHICLE", "confidence": "HIGH", "activities": [{"activityType": "IN_PASSENGER_VEHICLE", "probability": 91.75467491149902}, {"activityType": "WALKING", "probability": 3.579982742667198}, {"activityType": "STILL", "probability": 2.556953765451908}, {"activityType": "RUNNING", "probability": 0.9752147831022739}, {"activityType": "FLYING", "probability": 0.3199321450665593}, {"activityType": "IN_FERRY", "probability": 0.27216533198952675}, {"activityType": "CYCLING", "probability": 0.2206546487286687}, {"activityType": "IN_BUS", "probability": 0.13877064920961857}, {"activityType": "MOTORCYCLING", "probability": 0.06874967366456985}, {"activityType": "SKIING", "probability": 0.04611564800143242}, {"activityType": "SAILING", "probability": 0.037675429484806955}, {"activityType": "IN_TRAIN", "probability": 0.018856303358916193}, {"activityType": "IN_TRAM", "probability": 0.009571522241458297}, {"activityType": "IN_SUBWAY", "probability": 0.0006186741302371956}, {"activityType": "IN_VEHICLE", "probability": 6.746707299498667e-05}], "waypointPath": {"waypoints": [{"latE7": 359322586, "lngE7": -785680999}, {"latE7": 359850692, "lngE7": -786584625}, {"latE7": 359875450, "lngE7": -786779937}], "source": "INFERRED", "roadSegment": [{"placeId": "ChIJJ0iWnCpXrIkRXnn95UXltpk", "duration": "10s"}, {"placeId": "ChIJ29CSXypXrIkRmMB7AQopgxE", "duration": "23s"}, {"placeId": "ChIJjfcPSSpXrIkR09DD5urDtI4", "duration": "7s"}, {"placeId": "ChIJL-4XNCpXrIkRfw1zJr4u12k", "duration": "4s"}, {"placeId": "ChIJKehuLSpXrIkR7Jag2GnUeuc", "duration": "7s"}, {"placeId": "ChIJNQRf0ytXrIkRlZdV72FAynA", "duration": "2s"}, {"placeId": "ChIJpY6B1CtXrIkRGBUUDqdJ0q0", "duration": "1s"}, {"placeId": "ChIJBYdK1CtXrIkRzeOfji1u_Dk", "duration": "6s"}, {"placeId": "ChIJqwqxnylXrIkRtYL5Jelw5n0", "duration": "28s"}, {"placeId": "ChIJ2_efTyhXrIkRxb-dzMtl_Fw", "duration": "14s"}, {"placeId": "ChIJiTdIaChXrIkRhtO8bBT_xdk", "duration": "2s"}, {"placeId": "ChIJi7RDEihXrIkR0pxLKmIw6Dc", "duration": "14s"}, {"placeId": "ChIJE8uu7NdWrIkR0VPpjInW0w8", "duration": "3s"}, {"placeId": "ChIJBSuTr9dWrIkRo4ff2Qk8eOc", "duration": "9s"}, {"placeId": "ChIJ_yFt_9ZWrIkR5OR9X8Lozt8", "duration": "14s"}, {"placeId": "ChIJFVoQ8NZWrIkRmIm3k9zTaBA", "duration": "0s"}, {"placeId": "ChIJQzPD_9ZWrIkRBNWFomZyy4w", "duration": "5s"}, {"placeId": "ChIJw60KpddWrIkRvvUFU52XBXk", "duration": "9s"}, {"placeId": "ChIJTXEdlNdWrIkR95C9ptAMAb4", "duration": "5s"}, {"placeId": "ChIJ0zj-jtdWrIkR8IBGQhwxsYw", "duration": "9s"}, {"placeId": "ChIJLcmu1dlWrIkRZgDXKVPNeJ4", "duration": "1s"}, {"placeId": "ChIJe_fM0NlWrIkRTiu_kI97_L0", "duration": "3s"}, {"placeId": "ChIJ8RB329lWrIkR8BFrsdRHLEg", "duration": "4s"}, {"placeId": "ChIJc9JY79lWrIkRqrJwAn2Yln8", "duration": "2s"}, {"placeId": "ChIJ6dLu79lWrIkRffx0RF--KYE", "duration": "1s"}, {"placeId": "ChIJOXH-L9pWrIkRsgiATn3xCqU", "duration": "4s"}, {"placeId": "ChIJuyX8MNpWrIkRJgXOD4mPmnA", "duration": "1s"}, {"placeId": "ChIJp5vaNtpWrIkRMx7hCKBCb1o", "duration": "0s"}, {"placeId": "ChIJv3xfNtpWrIkRxR9iNcVfX2s", "duration": "1s"}, {"placeId": "ChIJ7RFhSdpWrIkRU_hOq7Gngf0", "duration": "4s"}, {"placeId": "ChIJJeZGUtpWrIkRzAODMTclAME", "duration": "2s"}, {"placeId": "ChIJwVUG_9pWrIkRBFYMBHSJEj0", "duration": "4s"}, {"placeId": "ChIJPXJ849pWrIkRdJKc4qODJm4", "duration": "5s"}, {"placeId": "ChIJCc1bSsVWrIkR21AJiHa88ig", "duration": "22s"}, {"placeId": "ChIJS28sm89WrIkR-LkKNRDe-jc", "duration": "11s"}, {"placeId": "ChIJl9i0HM9WrIkR_FcNoGKnTzA", "duration": "11s"}, {"placeId": "ChIJczgV3c5WrIkR5lThr9RtCN4", "duration": "0s"}, {"placeId": "ChIJNylvw85WrIkRYuxkPx2KVY0", "duration": "1s"}, {"placeId": "ChIJ1fhms85WrIkRX8E6x7VT6eA", "duration": "2s"}, {"placeId": "ChIJtSZuUclWrIkRRu3qc_wpVAU", "duration": "4s"}, {"placeId": "ChIJwZcW4stWrIkRuFPJm3bTvG8", "duration": "3s"}, {"placeId": "ChIJEZJo58tWrIkRDgA3jPNcF9o", "duration": "2s"}, {"placeId": "ChIJJxje3MtWrIkRFq186bR83bU", "duration": "0s"}, {"placeId": "ChIJJxje3MtWrIkRX4FJVDWK0kQ", "duration": "0s"}, {"placeId": "ChIJZ2MjtstWrIkRIUzcyjhtTyg", "duration": "6s"}, {"placeId": "ChIJUcbclspWrIkRF263fTqTJQ0", "duration": "4s"}, {"placeId": "ChIJwV-PocpWrIkRP9P-pf_FpWo", "duration": "3s"}, {"placeId": "ChIJ48WooMpWrIkR_bUqBc28gYs", "duration": "1s"}, {"placeId": "ChIJD7kTWbVWrIkRd2SMNcGc0Xw", "duration": "2s"}, {"placeId": "ChIJOXt3SbVWrIkRpX3vOBn5_KA", "duration": "9s"}, {"placeId": "ChIJB4Ry00pRrIkRRVzRg0BYRTw", "duration": "4s"}, {"placeId": "ChIJCxlFOktRrIkRW_M45PXuSgk", "duration": "11s"}, {"placeId": "ChIJURvinExRrIkRKwTh_r8APBk", "duration": "12s"}, {"placeId": "ChIJ2-cJ9kxRrIkR_Wk66UddWy0", "duration": "4s"}, {"placeId": "ChIJFbXcVUxRrIkRnvRXnckM1JA", "duration": "2s"}, {"placeId": "ChIJXSEcqU1RrIkRFhN0yZuKXRM", "duration": "5s"}, {"placeId": "ChIJTcsEok1RrIkRiBCv7ZyGK4M", "duration": "2s"}, {"placeId": "ChIJQ4ykm01RrIkRms_r_nafY5Q", "duration": "2s"}, {"placeId": "ChIJk2bfdlJRrIkRNuOl8thdH4A", "duration": "12s"}, {"placeId": "ChIJETMIQFJRrIkR9jiknwWTMfY", "duration": "1s"}, {"placeId": "ChIJwWx0OVJRrIkR5mcxt7_1R38", "duration": "2s"}, {"placeId": "ChIJadYfMlJRrIkRZUfE2q0Jbkk", "duration": "3s"}, {"placeId": "ChIJdcCw1lNRrIkR0fSX8FRSOw4", "duration": "4s"}, {"placeId": "ChIJbTGNYVFRrIkRNc42p36MRkA", "duration": "2s"}, {"placeId": "ChIJX6oxXFFRrIkRc1jpvzwI154", "duration": "3s"}, {"placeId": "ChIJm37-T1FRrIkRJc_yZLdCCs4", "duration": "2s"}, {"placeId": "ChIJQ3caTFFRrIkR_A0D0LbjxtI", "duration": "1s"}, {"placeId": "ChIJL8LRSlFRrIkRdCMNfBcEdAk", "duration": "1s"}, {"placeId": "ChIJa_VmylZRrIkRylKAhYtoFrQ", "duration": "1s"}, {"placeId": "ChIJgyhmyVZRrIkRCJXe5S2CsGY", "duration": "1s"}, {"placeId": "ChIJRfpyz1ZRrIkR5NiLJPRRptc", "duration": "2s"}, {"placeId": "ChIJW3ja2lZRrIkR3EwR_Mg1EZU", "duration": "1s"}, {"placeId": "ChIJE3v231ZRrIkRrg4x6BrvY8E", "duration": "6s"}, {"placeId": "ChIJM090GldRrIkRAqLIJLbLAgQ", "duration": "15s"}, {"placeId": "ChIJIebHEVdRrIkR8IfHvaG68Os", "duration": "3s"}, {"placeId": "ChIJ-8DjEVdRrIkRRNsFywP4Q0A", "duration": "2s"}, {"placeId": "ChIJGxwEsldRrIkRBO2tMXQPWPc", "duration": "22s"}, {"placeId": "ChIJSa-hIFZRrIkRinSwydCGg_4", "duration": "11s"}, {"placeId": "ChIJkz1r5f1TrIkRmipL2AzUpJg", "duration": "2s"}, {"placeId": "ChIJf8kK4v1TrIkR7uUGdzrraVk", "duration": "3s"}, {"placeId": "ChIJJ2gvFP5TrIkR0PSmWOHK6t8", "duration": "9s"}, {"placeId": "ChIJ80EfNP5TrIkRBglJqxkYDqE", "duration": "3s"}, {"placeId": "ChIJYYHq6_9TrIkRiGEmFAyNUzo", "duration": "13s"}, {"placeId": "ChIJMejSEwBUrIkR8pdCLXeaOog", "duration": "3s"}, {"placeId": "ChIJv6EjxgFUrIkRp0cSJsdVp1s", "duration": "18s"}, {"placeId": "ChIJC-HmYAJUrIkRIaD5DVjobJs", "duration": "13s"}, {"placeId": "ChIJB9HHuAJUrIkRvABtYWZNnO0", "duration": "9s"}, {"placeId": "ChIJe9nRch1UrIkRX91Ag01V0bk", "duration": "19s"}, {"placeId": "ChIJ1XJtSB5UrIkRs3fHyCE3tRw", "duration": "27s"}, {"placeId": "ChIJ_3oBISBUrIkRFAI8Hml16II", "duration": "2s"}, {"placeId": "ChIJb3gmXCBUrIkReRrUzvsSQsw", "duration": "15s"}, {"placeId": "ChIJ69G5xiBUrIkRQQ_CA_Ba5FA", "duration": "2s"}, {"placeId": "ChIJWbbYyCBUrIkR0LkSBWbiC00", "duration": "1s"}, {"placeId": "ChIJkaPPNidUrIkRZi-gpufHaK8", "duration": "5s"}, {"placeId": "ChIJRcJgxCdUrIkRJ7-16K0eCqU", "duration": "29s"}, {"placeId": "ChIJERcqsylUrIkRmzSGIqciTnA", "duration": "2s"}, {"placeId": "ChIJxzSJvClUrIkRHT_WXrAFh6g", "duration": "6s"}, {"placeId": "ChIJwS-IJypUrIkR3XdI40KRYaI", "duration": "6s"}, {"placeId": "ChIJ2YBsRipUrIkRSxvWRJFs1SI", "duration": "6s"}, {"placeId": "ChIJ3Zpz7ypUrIkRCWC67JcDrbc", "duration": "12s"}, {"placeId": "ChIJUV-_zSpUrIkRjdnYbPfuxdo", "duration": "5s"}, {"placeId": "ChIJnSZ1MdVVrIkRcaW_YZ7lQM0", "duration": "3s"}, {"placeId": "ChIJuQ11FtVVrIkRcUYxndvuiXU", "duration": "10s"}, {"placeId": "ChIJHx95oNVVrIkRnYrEbv5CTgI", "duration": "4s"}, {"placeId": "ChIJ3V9t4dVVrIkRjPO9zm0lVHk", "duration": "18s"}, {"placeId": "ChIJwwVALtZVrIkRVBsqIAeGKFU", "duration": "4s"}, {"placeId": "ChIJX1dr09dVrIkRdFakt3rO9Fg", "duration": "0s"}, {"placeId": "ChIJiWCr1tdVrIkRYrw7BDUIrPA", "duration": "1s"}, {"placeId": "ChIJm0BV2NdVrIkRSAL34nvCdqI", "duration": "2s"}, {"placeId": "ChIJ-3kY-ndWrIkRVpQkFn3XqxA", "duration": "17s"}, {"placeId": "ChIJl_WLSnZWrIkRglVszEyvZL8", "duration": "4s"}, {"placeId": "ChIJNRydrnVWrIkRhb7ttT7C8o0", "duration": "35s"}, {"placeId": "ChIJBZ_5tgtWrIkRQzf-mQa_D4g", "duration": "23s"}, {"placeId": "ChIJPdPkBwlWrIkRt6Cm0T7VqiM", "duration": "7s"}, {"placeId": "ChIJB5yJUAhWrIkR6eVHkJzXBXk", "duration": "4s"}, {"placeId": "ChIJy_XnQghWrIkRidUBjYWLQH8", "duration": "1s"}, {"placeId": "ChIJrSQVaQhWrIkRsS7KoE1f0ng", "duration": "2s"}, {"placeId": "ChIJ10PtbQhWrIkRoe7M-lSxlgw", "duration": "1s"}, {"placeId": "ChIJRULPdAhWrIkRFPIqNZovE9Q", "duration": "3s"}, {"placeId": "ChIJf8125gdWrIkRtNWiAygsnHI", "duration": "4s"}, {"placeId": "ChIJkepKgAdWrIkRwmvEMv2ajtg", "duration": "15s"}, {"placeId": "ChIJBy9LPf5VrIkR35PTctnj1lM", "duration": "23s"}, {"placeId": "ChIJmy4H8f9VrIkRMEn4sTR0_fE", "duration": "6s"}, {"placeId": "ChIJ5UPlRFX_rIkR0NCS9W862BM", "duration": "7s"}, {"placeId": "ChIJocLkOFX_rIkRwoR1X1Rc3Eg", "duration": "3s"}, {"placeId": "ChIJN9AzO1X_rIkRsjO--59NQN8", "duration": "3s"}, {"placeId": "ChIJ41Me2VT_rIkRlFIrMJdk04M", "duration": "6s"}, {"placeId": "ChIJI6mUxFT_rIkRGof6JDn1T6I", "duration": "0s"}, {"placeId": "ChIJ-QUQX1P_rIkRkEeX8sPemnE", "duration": "20s"}, {"placeId": "ChIJ4VMZ4FL_rIkRYH0fFpRyLd4", "duration": "4s"}, {"placeId": "ChIJk0YKb03_rIkRMhpBOeS5tJw", "duration": "27s"}, {"placeId": "ChIJ91nTjEz_rIkRJpi2spoX7XU", "duration": "3s"}, {"placeId": "ChIJg9j9gkz_rIkRSJjBqZH5UnE", "duration": "6s"}, {"placeId": "ChIJm29PG0v_rIkRQKODLNeq6m0", "duration": "17s"}, {"placeId": "ChIJ7_vCwEr_rIkRfDCOmRqgPj4", "duration": "10s"}], "distanceMeters": 18396.30339091307, "travelMode": "DRIVE", "confidence": 1.0}, "parkingEvent": {"location": {"latitudeE7": *********, "longitudeE7": -*********, "accuracyMetres": 61}, "method": "PERSONAL_VEHICLE_CONFIDENCE", "locationSource": "NOT_FROM_RAW_LOCATION", "timestamp": "2020-08-02T16:17:44.214Z"}}}, {"placeVisit": {"location": {"latitudeE7": *********, "longitudeE7": -*********, "placeId": "ChIJs_LRri3_rIkRS4_-ajb85OU", "address": "1432 Brogden Woods Dr Creedmoor Village\nWake Forest, NC 27587\nUSA", "name": "<PERSON>", "sourceInfo": {"deviceTag": -1285813491}, "locationConfidence": 92.877716, "calibratedProbability": 68.74247}, "duration": {"startTimestamp": "2020-08-02T16:10:28.500Z", "endTimestamp": "2020-08-02T16:17:49.153Z"}, "placeConfidence": "MEDIUM_CONFIDENCE", "centerLatE7": *********, "centerLngE7": -*********, "visitConfidence": 81, "otherCandidateLocations": [{"latitudeE7": *********, "longitudeE7": -*********, "placeId": "ChIJb9Zz8Cz_rIkRsWksfK0oeHA", "address": "1425 Brogden\nRemington Woods Dr\nWake Forest, NC 27587\nUSA", "name": "Bojangles' Famous Chicken 'n Biscuits", "locationConfidence": 4.1232977, "calibratedProbability": 3.0518155}, {"latitudeE7": 359900382, "longitudeE7": -786947352, "placeId": "ChIJe-ky8Sz_rIkRRAzJl9B8dNo", "address": "1440 Brogden Woods Drive\nSuite 103\nWake Forest, NC 27587\nUSA", "name": "Panda Garden", "locationConfidence": 0.846575, "calibratedProbability": 0.62658364}, {"latitudeE7": 359905336, "longitudeE7": -786934263, "placeId": "ChIJp_c0n2xRrIkRCgQcBQTHTb0", "address": "1428 Brogden Woods Dr Ste 101\nWake Forest, NC 27587\nUSA", "name": "Verizon Authorized Retailer - TCC", "locationConfidence": 0.69806886, "calibratedProbability": 0.5166684}, {"latitudeE7": 359906182, "longitudeE7": -786935800, "placeId": "ChIJBxfGrC3_rIkREvebUvw2gik", "address": "1428 Brogden Woods Drive #105\nWake Forest, NC 27587\nUSA", "name": "Best Cleaners", "locationConfidence": 0.45931476, "calibratedProbability": 0.33995703}, {"latitudeE7": 359900809, "longitudeE7": -786948001, "placeId": "ChIJs3Of9iz_rIkRVvh5BdEfFYY", "address": "1440 Brodgen Woods Drive Ste 104\nWake Forest, NC 27587\nUSA", "name": "<PERSON>'s Pizza", "locationConfidence": 0.45555764, "calibratedProbability": 0.33717623}], "editConfirmationStatus": "NOT_CONFIRMED", "locationConfidence": 78, "placeVisitType": "SINGLE_PLACE", "placeVisitImportance": "MAIN"}}, {"activitySegment": {"startLocation": {"latitudeE7": 359889016, "longitudeE7": -786941353, "sourceInfo": {"deviceTag": -1285813491}}, "endLocation": {"latitudeE7": 359914298, "longitudeE7": -787127830, "sourceInfo": {"deviceTag": -1285813491}}, "duration": {"startTimestamp": "2020-08-02T16:17:49.153Z", "endTimestamp": "2020-08-02T17:28:02.100Z"}, "distance": 6304, "activityType": "WALKING", "confidence": "LOW", "activities": [{"activityType": "WALKING", "probability": 45.049095153808594}, {"activityType": "RUNNING", "probability": 26.002439856529236}, {"activityType": "STILL", "probability": 17.773061990737915}, {"activityType": "IN_PASSENGER_VEHICLE", "probability": 9.700444340705872}, {"activityType": "SKIING", "probability": 0.5948834586888552}, {"activityType": "FLYING", "probability": 0.3365789772942662}, {"activityType": "IN_FERRY", "probability": 0.21251579746603966}, {"activityType": "CYCLING", "probability": 0.1742125954478979}, {"activityType": "SAILING", "probability": 0.11398571077734232}, {"activityType": "IN_BUS", "probability": 0.025546495453454554}, {"activityType": "MOTORCYCLING", "probability": 0.015647633699700236}, {"activityType": "IN_TRAM", "probability": 0.0011079522664658725}, {"activityType": "IN_TRAIN", "probability": 0.0002913214984801016}, {"activityType": "IN_SUBWAY", "probability": 0.00014713720020154142}, {"activityType": "IN_VEHICLE", "probability": 4.326725502323825e-05}], "waypointPath": {"waypoints": [{"latE7": 359889221, "lngE7": -786941375}, {"latE7": 359828109, "lngE7": -787175064}, {"latE7": 359906463, "lngE7": -787194290}, {"latE7": 359906463, "lngE7": -787194290}], "source": "INFERRED", "roadSegment": [{"placeId": "ChIJj1Dk3Sz_rIkR0iYFiahYXfM", "duration": "62s"}, {"placeId": "ChIJq1kBwCz_rIkRutZiMz6Pu78", "duration": "84s"}, {"placeId": "ChIJQYFMvSz_rIkRdAuML7no07U", "duration": "40s"}, {"placeId": "ChIJwYnblyz_rIkRXSH4FYZiROY", "duration": "12s"}, {"placeId": "ChIJQYFMvSz_rIkRUYF83C20K0w", "duration": "66s"}, {"placeId": "ChIJC78SoCz_rIkRnkCSaWnv8xE", "duration": "55s"}, {"placeId": "ChIJBQ55Xiv_rIkR9nWRWZ_Ggj4", "duration": "47s"}, {"placeId": "ChIJAx46Qyv_rIkRCt4-aY65bNQ", "duration": "56s"}, {"placeId": "ChIJoS-LOSv_rIkRt-3WEV9msFI", "duration": "143s"}, {"placeId": "ChIJT5q-1Sr_rIkRDQHAOdx28Lg", "duration": "135s"}, {"placeId": "ChIJCV-QMtX4rIkRfdAkx6onR40", "duration": "51s"}, {"placeId": "ChIJtywuNtX4rIkRwXeYspGum4o", "duration": "102s"}, {"placeId": "ChIJQ1LlRdX4rIkRYWijgyWpsF0", "duration": "16s"}, {"placeId": "ChIJnUIQmSr5rIkRbYVwkvk17uA", "duration": "435s"}, {"placeId": "ChIJd10sqyv5rIkRkfQvfPm8YWQ", "duration": "152s"}, {"placeId": "ChIJBUmhryv5rIkR94HyvEiuiYg", "duration": "11s"}, {"placeId": "ChIJp5GLvCv5rIkRo97o0H_vlcg", "duration": "163s"}, {"placeId": "ChIJq7F2kyv5rIkRf3CBfQnV4NY", "duration": "44s"}, {"placeId": "ChIJYVKq8iv5rIkR6yiwTXNCgEk", "duration": "47s"}, {"placeId": "ChIJM8LM9Sv5rIkR60GYn12513w", "duration": "110s"}, {"placeId": "ChIJc14FCSz5rIkRE4Q4SPmR73w", "duration": "16s"}, {"placeId": "ChIJ379jGiz5rIkRoz__1vkR4K4", "duration": "165s"}, {"placeId": "ChIJId8EDC75rIkRXXZNuOKUYCQ", "duration": "634s"}, {"placeId": "ChIJ-b3CmDH5rIkRWiBxJpxCEl4", "duration": "102s"}, {"placeId": "ChIJN2n0EzH5rIkRGOVXlTgxqaU", "duration": "49s"}, {"placeId": "ChIJO0NjFTH5rIkRIH5atepOQ-A", "duration": "66s"}], "distanceMeters": 3250.107352893875, "travelMode": "WALK", "confidence": 0.0}, "simplifiedRawPath": {"points": [{"latE7": *********, "lngE7": -*********, "accuracyMeters": 66, "timestamp": "2020-08-02T16:23:43.955Z"}]}}}, {"placeVisit": {"location": {"latitudeE7": *********, "longitudeE7": -*********, "placeId": "ChIJMc2y3vj-rIkRpzd2RjOPPs8", "address": "Falls Lake\nNorth Carolina\nUSA", "name": "Falls Lake", "sourceInfo": {"deviceTag": -1285813491}, "locationConfidence": 26.622845, "calibratedProbability": 8.603858}, "duration": {"startTimestamp": "2020-08-02T17:28:02.100Z", "endTimestamp": "2020-08-02T17:36:13.326Z"}, "placeConfidence": "LOW_CONFIDENCE", "centerLatE7": *********, "centerLngE7": -*********, "visitConfidence": 71, "otherCandidateLocations": [{"latitudeE7": *********, "longitudeE7": -*********, "placeId": "ChIJEd1HotL-rIkRa-RiVl1mCEc", "address": "13586 Boyce Mill Rd\nDurham, NC 27703\nUSA", "name": "13586 Boyce Mill Rd", "locationConfidence": 20.48827, "calibratedProbability": 6.621312}, {"latitudeE7": 359739337, "longitudeE7": -787277804, "placeId": "ChIJ_wb91jb5rIkR_lI5e_kefiU", "address": "Laurel Creek\nNorth Carolina\nUSA", "name": "Laurel Creek", "locationConfidence": 19.731085, "calibratedProbability": 6.3766084}, {"latitudeE7": 359924890, "longitudeE7": -787125620, "placeId": "ChIJMWfUZdL-rIkRWD87Tikl-7s", "address": "13582 Boyce Mill Rd\nDurham, NC 27703\nUSA", "name": "13582 Boyce Mill Rd", "locationConfidence": 18.720758, "calibratedProbability": 6.0500956}, {"latitudeE7": 359894240, "longitudeE7": -787149580, "placeId": "ChIJFSFqtTL5rIkRQ9lBONKqJhI", "address": "13578 Boyce Mill Rd\nDurham, NC 27703\nUSA", "name": "13578 Boyce Mill Rd", "locationConfidence": 14.437042, "calibratedProbability": 4.6657023}], "editConfirmationStatus": "NOT_CONFIRMED", "locationConfidence": 19, "placeVisitType": "SINGLE_PLACE", "placeVisitImportance": "MAIN"}}]}