import random
from time import sleep

from orca_sdk import (
    ClassificationModel,
    Datasource,
    LabeledMemoryset,
    OrcaCredentials,
    PretrainedEmbeddingModel,
)

from settings.app_secrets import secrets

data = [
    {
        "score": 3,
        "value": "I felt hopeless for long stretches and cried without warning, after reading a message from best friend I reminded myself this feeling can pass.",
    },
    {
        "score": 3,
        "value": "I kept thinking I’m a burden to everyone, on the ride home from the doctor, staring out the window I promised myself to check in again tomorrow.",
    },
    {
        "score": 0,
        "value": "I had fun and felt like myself, as the rain rolled in and I noticed my breathing I ended the night journaling about it.",
    },
    {
        "score": 4,
        "value": "I’m exhausted by existing and everything hurts, during a quiet lunch alone where I felt invisible I can’t stop crying and I don’t know why.",
    },
    {
        "score": 2,
        "value": "I felt on edge and kept second-guessing myself, during a checkup appointment where I felt awkward I took a walk and watched the sky for a while.",
    },
    {
        "score": 1,
        "value": "I’m cautiously optimistic even though energy was low, during a family dinner where I felt invisible I set a tiny goal and tried to follow through.",
    },
    {
        "score": 4,
        "value": "I feel like giving up on everything today, when I thought about the coming year and what comes next I feel like there is no reason to keep trying.",
    },
    {
        "score": 0,
        "value": "I felt grateful and optimistic about what’s next, on the ride home from the store, staring out the window I reminded myself this feeling can pass.",
    },
    {
        "score": 0,
        "value": "I’m proud of how I handled things, after a short walk and reflecting on the past week I promised myself to check in again tomorrow.",
    },
    {
        "score": 2,
        "value": "I felt distant from friends and didn’t engage much, on the ride home from therapy, staring out the window I took a walk and watched the sky for a while.",
    },
    {
        "score": 1,
        "value": "I felt a bit tense but also productive, during a checkup appointment where I felt awkward I kept it to myself because I didn’t want to worry anyone.",
    },
    {
        "score": 2,
        "value": "I stayed in bed longer than I wanted, dragging through the morning, during a quiet lunch alone where I felt invisible I kept it to myself because I didn’t want to worry anyone.",
    },
    {
        "score": 4,
        "value": "I feel completely hopeless like nothing will ever change, on the ride home from the store, staring out the window I can’t carry this weight anymore and I’m scared.",
    },
    {
        "score": 0,
        "value": "I felt calm, steady, and content, while walking through the park this late night I noticed my body felt tense all over.",
    },
    {
        "score": 3,
        "value": "I felt like I was failing at basic things, after stretching and reflecting on the past week I wrote it down so I wouldn’t forget how it felt.",
    },
    {
        "score": 0,
        "value": "I had a productive and happy day, while walking through the balcony this evening I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m a bit stressed, yet still hopeful, on the ride home from school, staring out the window I took a walk and watched the sky for a while.",
    },
    {
        "score": 3,
        "value": "I felt empty and disconnected from everything, on the ride home from therapy, staring out the window I noticed my body felt tense all over.",
    },
    {
        "score": 0,
        "value": "I’m optimistic about the future, while walking through the kitchen this morning I wrote it down so I wouldn’t forget how it felt.",
    },
    {
        "score": 3,
        "value": "I avoided people because I felt ashamed and exhausted, after stretching and reflecting on the past week I wrote it down so I wouldn’t forget how it felt.",
    },
    {
        "score": 0,
        "value": "I felt connected and present with people around me, after reading a message from colleague I kept it to myself because I didn’t want to worry anyone.",
    },
    {
        "score": 4,
        "value": "I feel broken inside and beyond repair, during a video call where I felt awkward I can’t carry this weight anymore and I’m scared.",
    },
    {
        "score": 3,
        "value": "I’m terrified about the future and felt stuck, during a team meeting where I felt awkward I noticed my body felt tense all over.",
    },
    {
        "score": 1,
        "value": "I had some doubts but stayed functional, during a quiet lunch alone where I felt awkward I reminded myself this feeling can pass.",
    },
    {"score": 3, "value": "I feel so lonely even in a crowd"},
    {
        "score": 4,
        "value": "I can’t imagine things improving and I’m terrified, during a quiet lunch alone where I felt invisible I feel like giving up on everything today",
    },
    {
        "score": 4,
        "value": "I’m better off disappearing, during a family dinner where I felt invisible I can’t carry this weight anymore and I’m scared.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for getting dressed today, as I cleaned my desk and tried to keep my mind busy I promised myself to check in again tomorrow.",
    },
    {
        "score": 3,
        "value": "I kept replaying mistakes and felt overwhelmed, on the ride home from school, staring out the window I kept it to myself because I didn’t want to worry anyone.",
    },
    {
        "score": 3,
        "value": "I felt a constant knot in my chest and stomach, during a checkup appointment where I felt awkward I reminded myself this feeling can pass.",
    },
    {
        "score": 0,
        "value": "I enjoyed small moments, like coffee in the sun, after reading a message from therapist I took a walk and watched the sky for a while.",
    },
    {
        "score": 4,
        "value": "I’m stuck in darkness and can’t find a way out, when I thought about my goals and what comes next I feel like giving up on everything today",
    },
    {
        "score": 3,
        "value": "I kept zoning out and forgetting what I was doing, after a nap and reflecting on the past week I noticed my body felt tense all over.",
    },
    {
        "score": 2,
        "value": "I felt overwhelmed by simple tasks, on the ride home from therapy, staring out the window I reminded myself this feeling can pass.",
    },
    {
        "score": 4,
        "value": "I kept it to myself because I didn’t want to worry anyone, I’m exhausted by existing and everything hurts, during a team meeting where I felt invisible",
    },
    {
        "score": 2,
        "value": "I felt numb, on the ride home from the doctor, staring out the window I noticed my body felt tense all over.",
    },
    {
        "score": 4,
        "value": "I feel utterly alone even when someone is near me, after reading a message from partner I can’t stop crying and I don’t know why.",
    },
    {
        "score": 2,
        "value": "I felt small and unimportant today, after a nap and reflecting on the past week I noticed my body felt tense all over.",
    },
    {
        "score": 3,
        "value": "I felt terrified about the future and felt stuck, on the ride home from work, staring out the window I promised myself to check in again tomorrow.",
    },
    {
        "score": 2,
        "value": "I felt heavy and unmotivated for most of the day, after a short walk and reflecting on the past week I wrote it down so I wouldn’t forget how it felt.",
    },
    {
        "score": 4,
        "value": "I felt like giving up on everything today, while walking through the balcony this late night I reminded myself this feeling can pass.",
    },
    {
        "score": 0,
        "value": "I laughed a lot and felt grounded, during a family dinner where I felt seen I reminded myself this feeling can pass.",
    },
    {
        "score": 2,
        "value": "I avoided messages because I couldn’t handle talking, after reading a message from mom I wrote it down so I wouldn’t forget how it felt.",
    },
    {
        "score": 4,
        "value": "I can’t stop crying and I don’t know why, when I thought about my goals and what comes next I feel like giving up on everything today",
    },
    {
        "score": 3,
        "value": "I felt worthless and couldn’t stop the spiral, on the ride home from therapy, staring out the window I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for showing up to work, during a team meeting where I felt supported I reminded myself this feeling can pass.",
    },
    {"score": 0, "value": "I had a great talk with a friend today, feeling supported"},
    {
        "score": 1,
        "value": "I’m proud of myself for making dinner, as I cooked dinner and tried to keep my mind busy I took a walk and watched the sky for a while.",
    },
    {
        "score": 1,
        "value": "I had some doubts but stayed functional, during a video call where I felt supported I reminded myself this feeling can pass.",
    },
    {
        "score": 3,
        "value": "I’m terrified about the future and felt stuck, when I thought about my goals and what comes next I promised myself to check in again tomorrow.",
    },
    {
        "score": 2,
        "value": "I stayed in bed longer than I wanted, dragging through the morning, while walking through the gym this afternoon I kept it to myself because I didn’t want to worry anyone.",
    },
    {"score": 0, "value": "I enjoyed playing with my kids"},
    {
        "score": 1,
        "value": "I felt proud of myself for reaching out to a friend, on the ride home from therapy, staring out the window I noticed my body felt tense all over.",
    },
    {
        "score": 3,
        "value": "I avoided people because I felt ashamed and exhausted, after reading a message from therapist I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m cautiously optimistic even though energy was low, during a checkup appointment where I felt supported I reminded myself this feeling can pass.",
    },
    {
        "score": 0,
        "value": "I felt optimistic about the future, on the ride home from the store, staring out the window I promised myself to check in again tomorrow.",
    },
    {
        "score": 4,
        "value": "I’m better off disappearing, while walking through the balcony this afternoon I feel like there is no reason to keep trying.",
    },
    {
        "score": 1,
        "value": "I had mixed feelings but nothing overwhelming, after a nap and reflecting on the past week I wrote it down so I wouldn’t forget how it felt.",
    },
    {
        "score": 3,
        "value": "I kept zoning out and forgetting what I was doing, on the ride home from work, staring out the window I kept it to myself because I didn’t want to worry anyone.",
    },
    {
        "score": 0,
        "value": "I’m proud of how I handled things, during a team meeting where I felt supported I noticed my body felt tense all over.",
    },
    {
        "score": 3,
        "value": "I kept thinking I’m a burden to everyone, on the ride home from therapy, staring out the window I kept it to myself because I didn’t want to worry anyone.",
    },
    {
        "score": 0,
        "value": "I enjoyed small moments, like coffee in the sun, during a video call where I felt supported I reminded myself this feeling can pass.",
    },
    {
        "score": 0,
        "value": "I felt supported and safe today, as the thunderstorm rolled in and I noticed my breathing I noticed my body felt tense all over.",
    },
    {"score": 4, "value": "I feel broken inside"},
    {
        "score": 1,
        "value": "I’m quietly proud of myself for brushing my teeth today, during a family dinner where I felt supported I reminded myself this feeling can pass.",
    },
    {"score": 4, "value": "I feel like giving up on everything"},
    {"score": 4, "value": "I can’t imagine things ever improving"},
    {
        "score": 1,
        "value": "I’m quietly proud of myself for making the bed, as I cooked dinner and tried to keep my mind busy I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m cautiously optimistic even though energy was low, after reading a message from therapist I reminded myself this feeling can pass.",
    },
    {"score": 4, "value": "I’m exhausted by existing and everything hurts"},
    {
        "score": 4,
        "value": "I feel like there is no reason to keep trying, when I thought about the coming year and what comes next I feel like giving up on everything today",
    },
    {
        "score": 1,
        "value": "I had some doubts but stayed functional, as I cooked dinner and tried to keep my mind busy I kept it to myself because I didn’t want to worry anyone.",
    },
    {
        "score": 3,
        "value": "I’m terrified about the future and felt stuck, during a quiet lunch alone where I felt awkward I reminded myself this feeling can pass.",
    },
    {
        "score": 3,
        "value": "I kept replaying mistakes and felt overwhelmed, on the ride home from school, staring out the window I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for doing the dishes, while walking through the balcony this late night I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I had some doubts but stayed functional, during a checkup appointment where I felt supported I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for making a to-do list, during a team meeting where I felt seen I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for putting on clean clothes, while walking through the balcony this late night I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for texting back today, after reading a message from best friend I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for eating something healthy, as I took a shower and tried to keep my mind busy I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for doing a load of laundry, during a family dinner where I felt supported I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for speaking up in a meeting, during a team meeting where I felt seen I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for going outside today, while walking through the park this afternoon I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for finishing a small task, after a short walk and reflecting on the past week I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for brushing my hair, as I did laundry and tried to keep my mind busy I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for checking my email, during a team meeting where I felt supported I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for drinking water, as I journaled and tried to keep my mind busy I reminded myself this feeling can pass.",
    },
    {
        "score": 1,
        "value": "I’m quietly proud of myself for making the bed, after a nap and reflecting on the past week I reminded myself this feeling can pass.",
    },
]


class OrcaDB:
    def __init__(self, api_key: str):
        OrcaCredentials.set_api_key(api_key=api_key)
        assert OrcaCredentials.is_authenticated()

    def _initialize_memoryset(
        self, model_id: str, data_id: str, data: list[dict], scoring_field: str, value_field: str
    ) -> LabeledMemoryset:
        ClassificationModel.drop(name_or_id=model_id, if_not_exists="ignore")
        Datasource.drop(name_or_id=f"{data_id}_datasource", if_not_exists="ignore")
        LabeledMemoryset.drop(name_or_id=data_id, if_not_exists="ignore")
        sleep(2)
        return LabeledMemoryset.from_list(
            name=data_id,
            data=data,
            label_column=scoring_field,
            value_column=value_field,
            embedding_model=PretrainedEmbeddingModel.E5_LARGE,
            label_names=["0", "1", "2", "3", "4"],
        )

    def initialize_model(
        self, model_id: str, data_id: str, data: list[dict], scoring_field: str, value_field: str
    ) -> ClassificationModel:
        memory_set = self._initialize_memoryset(
            model_id=model_id, data_id=data_id, data=data, scoring_field=scoring_field, value_field=value_field
        )
        if ClassificationModel.exists(name_or_id=model_id):
            return ClassificationModel.open(name=model_id)
        return ClassificationModel.create(name=model_id, memoryset=memory_set, if_exists="open")


if __name__ == "__main__":
    orcadb = OrcaDB(api_key=secrets.ORCADB_API_KEY)

    model = orcadb.initialize_model(
        model_id="class_model",
        data_id="class_data",
        data=data,
        scoring_field="score",
        value_field="value",
    )
    inp = {"value": "I am feeling the worst today", "score": 4}
    prediction = model.predict(value=inp["value"])
    print(prediction)

    print(f"input: {inp}\n prediction: {prediction.label}, expected: {inp['score']}")

    testing_data = random.choices(k=30, population=data)
    Datasource.drop(name_or_id="class_datasource", if_not_exists="ignore")
    eval = model.evaluate(
        data=Datasource.from_list(name="class_datasource", data=testing_data),
        value_column="value",
        label_column="score",
    )
    print(eval)
