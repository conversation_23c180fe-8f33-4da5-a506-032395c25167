import json
import random
from datetime import datetime, timedelta
from time import sleep
from typing import Literal
from uuid import UUID
from zoneinfo import ZoneInfo

from orca_sdk import (
    Datasource,
    OrcaCredentials,
    PretrainedEmbeddingModel,
    RegressionModel,
    ScoredMemoryset,
)

from services.base.application.boundaries.time_input import TimeRangeInput
from services.base.domain.schemas.air_quality import AirQuality, AirQualityFields
from services.base.domain.schemas.events.symptom import SymptomCategory, SymptomFields
from services.base.domain.schemas.pollen import Pollen, PollenFields
from services.base.domain.schemas.shared import BaseDataModel
from settings.app_secrets import secrets


class RegressDataSeriesUseCaseInputBoundary(BaseDataModel):
    node: Literal[SymptomCategory.RESPIRATORY_COUGH]
    user_id: UUID


class RegressDataSeriesUseCaseOutputBoundary(BaseDataModel):
    value: float


class RegressDataSeriesUseCase:
    def __init__(self, orcadb):
        self._orcadb = orcadb

    async def execute_async(
        self, input_boundary: RegressDataSeriesUseCaseInputBoundary
    ) -> RegressDataSeriesUseCaseOutputBoundary:
        # Requires way to access quantifying field of each node
        field_name = SymptomFields.RATING
        now = datetime.now(ZoneInfo("UTC"))
        bulgarian_td = timedelta(hours=12)

        last_regression_at = await self.last_regression_at(user_id=input_boundary.user_id, node=input_boundary.node)
        regress_since = last_regression_at or (now - timedelta(days=90))

        dataset = await self.lookup(
            node=input_boundary.node,
            user_id=input_boundary.user_id,
            time_input=TimeRangeInput(time_gte=regress_since, time_lte=now - bulgarian_td),
        )

        # aggregate the most recent data that we want to use to predict
        query = await self.lookup(
            node=input_boundary.node,
            user_id=input_boundary.user_id,
            time_input=TimeRangeInput(time_gte=now - bulgarian_td, time_lte=now),
        )

        prediction = self._orcadb.predict(
            queries=query, model_id="test_model", data_id="test_data", scoring_field=field_name, data=dataset
        )

        return RegressDataSeriesUseCaseOutputBoundary(value=prediction.score)

    async def last_regression_at(self, user_id: UUID, node: SymptomCategory) -> datetime | None:
        # keep track of when the given was analyzed for given user
        # technically we might need to swap factors for the current ones if it changed

        return None

    async def lookup(self, user_id: UUID, node: SymptomCategory, time_input: TimeRangeInput) -> list[dict]:
        # Prepares data to be used for regressing next value
        # Requires way to access most impactful factors for given user - for each, get Node-Field/Occurrence-TimeLag(From,To)-AggregationMethod
        factors = [
            (AirQuality, AirQualityFields.PM10, (timedelta(seconds=0), timedelta(hours=24)), "sum"),
            (AirQuality, AirQualityFields.PM25, (timedelta(seconds=0), timedelta(hours=24)), "sum"),
            (AirQuality, AirQualityFields.AQI, (timedelta(seconds=0), timedelta(hours=24)), "max"),
            (Pollen, PollenFields.TREE, (timedelta(seconds=0), timedelta(hours=24)), "sum"),
            (Pollen, PollenFields.WEED, (timedelta(seconds=0), timedelta(hours=24)), "sum"),
            (Pollen, PollenFields.GRASS, (timedelta(seconds=0), timedelta(hours=24)), "sum"),
        ]

        # gather the data per factors into aggregated rows
        factors
        return []


class OrcaDB:
    def __init__(self, api_key: str):
        OrcaCredentials.set_api_key(api_key=api_key)
        assert OrcaCredentials.is_authenticated()

    def _initialize_memoryset(
        self, model_id: str, data_id: str, data: list[dict], scoring_field: str, value_field: str
    ) -> ScoredMemoryset:
        RegressionModel.drop(name_or_id=model_id, if_not_exists="ignore")
        Datasource.drop(name_or_id=f"{data_id}_datasource", if_not_exists="ignore")
        ScoredMemoryset.drop(name_or_id=data_id, if_not_exists="ignore")
        sleep(2)
        return ScoredMemoryset.from_list(
            name=data_id,
            data=data,
            score_column=scoring_field,
            value_column=value_field,
            embedding_model=PretrainedEmbeddingModel.E5_LARGE,
        )

    def initialize_model(
        self, model_id: str, data_id: str, data: list[dict], scoring_field: str, value_field: str
    ) -> RegressionModel:
        memory_set = self._initialize_memoryset(
            model_id=model_id, data_id=data_id, data=data, scoring_field=scoring_field, value_field=value_field
        )
        if RegressionModel.exists(name_or_id=model_id):
            return RegressionModel.open(name=model_id)
        return RegressionModel.create(name=model_id, memoryset=memory_set, if_exists="open", memory_lookup_count=10)


if __name__ == "__main__":
    user_data = json.load(open("environment_triggers_user_2.json", "r"))
    data = [
        {
            "score": entry["sleep_score"],
            "value": json.dumps({k: v for k, v in entry.items() if k != "sleep_score"}),
        }
        for entry in user_data
    ]

    orcadb = OrcaDB(api_key=secrets.ORCADB_API_KEY)

    orcadb.initialize_model(
        model_id="test_model",
        data_id="test_data",
        data=data,
        scoring_field="score",
        value_field="value",
    )
    model = RegressionModel.open(name="test_model")

    testing_data = random.choices(k=65, population=data)
    rand_in = random.choice(data)
    prediction = model.predict(value=rand_in["value"])

    print(f"input: {rand_in}\n prediction: {prediction.score}, expected: {rand_in['score']}")

    Datasource.drop(name_or_id="test_datasource", if_not_exists="ignore")
    eval = model.evaluate(
        data=Datasource.from_list(name="test_datasource", data=testing_data),
        value_column="value",
        score_column="score",
    )
    print(eval)
