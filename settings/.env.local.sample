RUN_ENV=local
USE_SSL=False
DOMAIN=http://localhost:3000

AFTER_LOGIN_REDIRECT_URL=${DOMAIN}/login/oauth_callback
AFTER_LOGIN_IOS_REDIRECT_URL=${DOMAIN}/login/ios_oauth_callback

# LOGGING
LOG_LEVEL=DEBUG

# DATA LOADING
DATA_SOURCE_DIR=seed_data/samples

# DATABASE
OS_HOSTS=["http://os01:9200"]
OS_HOST_LOCAL=http://localhost:9200
SPLIT_INDEX_POSTFIX_FORMAT=yyyy
SPLIT_INDEX_POSTFIX_TEMPLATE=%Y
OS_PRIMARY_SHARDS_COUNT=1
OS_REPLICA_SHARDS_COUNT=0
OS_MAXIMUM_DOCUMENT_COUNT=100_000_000
OS_MAXIMUM_INDEX_STORAGE=10gb
OS_BULK_MAX_LINES_PER_COMMIT=2000
OS_BULK_MAX_SIZE_PER_COMMIT=10485760

POSTGRES_DB=postgres
POSTGRES_HOST=db01
POSTGRES_PORT=5432
POSTGRES_PASSWORD=postgres
POSTGRES_USER=postgres

# AUTH
ACCESS_TOKEN_SECRET=SjqtZs4x3FMH8bJNMYz2ZyD8gZU+cbjmC1uJ3HQ37TI=
REFRESH_TOKEN_SECRET=ZlBmI7/szwykesG0i3AAcC6k7BlqQLGqjmLQNyERnO4=
LOGIN_MAIL_WHITELIST=["<EMAIL>"]
API_ACCESS_TOKEN_EXPIRATION_TIME_MINUTES=10080
API_REFRESH_TOKEN_EXPIRATION_TIME_DAYS=30
API_REFRESH_TOKEN_ROTATION_TIME_DAYS=7

# PROVIDERS
GOOGLE_OAUTH2_WEB_CLIENT_ID=837633548436-ur5lctdbtmt0mpcr39h4187tmfb2qmo4.apps.googleusercontent.com
GOOGLE_OAUTH2_SECRET=secret
GOOGLE_OAUTH2_IOS_CLIENT_ID=837633548436-itutl8v3oj6h971ib2406iaii3ns00o3.apps.googleusercontent.com
GOOGLE_OAUTH2_CODE_VERIFIER=gwVXvhBow3.Qne9K7IwND_kqGfpd-bDP0LrhbFWc9hg~n237vOErAJm3PlJjKJ2jAmVlJ29NHb1dQzCr~j9ClZDZTBteJgcq67Dvc-jAXSBBp9j7It~dkrhczeHuzMCa
GOOGLE_OAUTH2_CODE_CHALLENGE=JjeFuxPvX8foLesUIAZ7fWnpPYmxqe7SRQv_pSLfw8k

APPLE_OAUTH2_MOBILE_CLIENT_ID=org.llif.mylife.dev
APPLE_OAUTH2_WEB_CLIENT_ID=org.llif.mylife.dev.web

FITBIT_OAUTH2_WEB_CLIENT_ID=2389HG
FITBIT_OAUTH2_WEB_CLIENT_SECRET=secret
FITBIT_OAUTH2_CODE_VERIFIER=QyhaZ3xGgORsrDkfYOT35v03QOiEx125c6sVYnvVaUMBqog055l-qtJW41OE7PhPOjvUfiYm-9TPNLy3-hJmiBqRp6Un_zdEZqAxHnsoZ5d2.JVzdEOiYCxW5T9RIIhT
FITBIT_OAUTH2_CODE_CHALLENGE=R6EQwdIRPBuJI7lepU6WvpZsVQbAE58W09H6OUJ_iRI

AMAZON_OAUTH2_WEB_CLIENT_ID=amzn1.application-oa2-client.f6e716694fd145dcba80fa494c64b36d
AMAZON_OAUTH2_WEB_CLIENT_SECRET=secret

# AWS
AWS_REGION_NAME=us-east-1
AWS_SELF_HOSTED=True
AWS_URL=http://localstack:4566
AWS_URL_LOCAL=http://localhost:4566
# Necessary to override CDKLOCAL localstack endpoint, redundant on remote environments that interact directly with AWS
LOCALSTACK_HOSTNAME=localstack
# Only uncomment if you have valid localstack key to use!
#LOCALSTACK_API_KEY=

# OBJECT STORAGE
ASSETS_DIRECTORY=assets
EXPORTS_DIRECTORY=exports
ASSET_STORAGE_CREDENTIALS="DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=%s/devstoreaccount1;"
ASSET_STORAGE_URL=http://azurite:10000

# S3
S3_BUCKET_NAME=llifmemberupload
S3_MOUNT_DIRECTORY=/s3

# ENCRYPTION
AUTHORIZATION_ENCRYPTION_KEY=adDqje-tSjmz9f7rPIsHebTB1voKhElSJ3JF8rzPRiE=
RECOVERY_ENCRYPTION_KEY=NeMpiAjb5CFfMO0H2jWtDnphmr4ckXm48PNWp1-ijvg=

# FIREBASE
FIREBASE_CREDENTIALS={}

# ENVIRONMENT
RADIUS=10km
AMBEE_RATELIMIT=10000
WEATHERAPI_RATELIMIT=322000
OPENAQ_RATELIMIT=86000
OPENMETEO_RATELIMIT=10000
AMBEE_APIKEY=secret
WAPI_KEY=secret
VISUALCROSSING_APIKEY=secret

# HASH KEYS
PUBLIC_UUID_HASH_KEY=PxJwM_4MtRjMS2j2CvSXKGYAE6bzc2sTgwnftIDY_8U=

# ZOHO
ZOHO_REFRESH_TOKEN=**********************************************************************
ZOHO_CLIENT_ID=1000.VJ6K864WWMJ4U73ZVJ9BZ6AQT0OE2G
ZOHO_CLIENT_SECRET=4387ed5bab87dff1f3f1c96873cad8df6d0523c93e

# AWS Resources
ENV_LABEL=env
ENVIRONMENT_PREFIX=local_
AWS_ACCOUNT=************
ALEXA_VOICE_LOG_ID=amzn1.ask.skill.xx-xx-xx-xx-xx
ALEXA_VOICE_LOG_NAME=Best Life Local

# Topics
TOPIC_ANALYSIS_SCHEDULED=analytic_scheduled
TOPIC_ANALYSIS_FINISHED=analytic_finished
TOPIC_MEMBER_USER_REGISTERED=member_user_registered
TOPIC_TAKEOUT_EXPORT_FINISHED=takeout_export_finished
TOPIC_MEMBER_USER_ACCOUNT_LINKED=member_user_account_linked

# Queues
QUEUE_SINGLE_CORRELATION_JOB=single_correlation_job_queue
QUEUE_TREND_INSIGHTS_JOB=trend_insights_job_queue
QUEUE_NOTIFY_HANDLER_JOB=notify_handler_job_queue

# Telemetry
TELEMETRY_TRACES_SAMPLE_RATE=1.0
TELEMETRY_PROFILE_SAMPLE_RATE=1.0
TELEMETRY_REQUEST_BODIES=medium
MY_LLIF_SENTRY_DSN=secret
MY_LLIF_APPS_SENTRY_DSN=secret
MY_LLIF_LOGFIRE_WRITE_TOKEN=secret

# UseCases
REMIND_ACTIVITY_LOCAL_HOUR=19
REMIND_PLANS_LOCAL_HOUR=3
DAYS_CONSIDERED_ACTIVE=30

# Extensions
TREND_INSIGHTS_EXTENSION_ID=d26245a6-16fe-4a96-a4dc-bc31d9336e2a
TREND_INSIGHTS_EXTENSION_SCHEDULE=daily
SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID=f36589a6-16fe-4a96-a4dc-bc31d9336e2a
SINGLE_CORRELATION_EVALUATOR_SCHEDULE=daily

# Clients
BEST_LIFE_APP_ID=a67e3661-2d53-451b-8095-75589fc0e452

# System
MY_LLIF_FILE_SERVICE_ID=********-4a5e-4583-9a54-3e8ab45e7f5b
MY_LLIF_ID=0099d1b3-dcfd-4d6f-9b60-1986021a8dc4

# Slack Channels
COST_NOTIFICATION_CHANNEL_ID=C075LDYK2SW
COST_NOTIFICATION_BOT_TOKEN=secret

# YOUTUBE
YOUTUBE_API_KEY=x

# AI
OPENAI_API_KEY=secret
GROQ_API_KEY=secret

# Lookup
USDA_API_KEY=secret
USDA_LOOKUP_URL=https://api.nal.usda.gov/fdc/v1/foods/search

# TEMPORAL
TEMPORAL_HOST=temporal
TEMPORAL_PORT=7233
