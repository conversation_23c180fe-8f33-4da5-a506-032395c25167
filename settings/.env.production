RUN_ENV=production
USE_SSL=True
API_DOMAIN=api.my.llif.org

DOMAIN=https://my.llif.org

AFTER_LOGIN_REDIRECT_URL=${DOMAIN}/login/oauth_callback
AFTER_LOGIN_IOS_REDIRECT_URL=${DOMAIN}/login/ios_oauth_callback

# LOGGING
LOG_LEVEL=ERROR

# DATA LOADING
DATA_SOURCE_DIR=seed_data/samples

# DATABASE
OS_HOSTS=["https://vpc-demo1-6xtuhdy4xnu3hruxrazdsbvxlq.us-east-1.es.amazonaws.com"]
SPLIT_INDEX_POSTFIX_FORMAT=yyyy-MM
SPLIT_INDEX_POSTFIX_TEMPLATE=%Y-%m
OS_PRIMARY_SHARDS_COUNT=4
OS_REPLICA_SHARDS_COUNT=1
OS_MAXIMUM_DOCUMENT_COUNT=1_000_000_000
OS_MAXIMUM_INDEX_STORAGE=50gb
OS_BULK_MAX_LINES_PER_COMMIT=2000
OS_BULK_MAX_SIZE_PER_COMMIT=10485760

POSTGRES_DB=postgres
POSTGRES_HOST=llif-production.czujeup6ajfd.us-east-1.rds.amazonaws.com
POSTGRES_PORT=5432

# AUTH
API_ACCESS_TOKEN_EXPIRATION_TIME_MINUTES=2
API_REFRESH_TOKEN_EXPIRATION_TIME_DAYS=20
API_REFRESH_TOKEN_ROTATION_TIME_DAYS=10

# AWS
AWS_SELF_HOSTED=False
AWS_REGION_NAME=us-east-1

# OBJECT STORAGE
ASSETS_DIRECTORY=assets
EXPORTS_DIRECTORY=exports

# S3
S3_BUCKET_NAME=llifmemberupload
S3_MOUNT_DIRECTORY=/s3

# PROVIDERS
GOOGLE_OAUTH2_WEB_CLIENT_ID=************-god6pliaaum2tm3b2mhc0gj7focll3uk.apps.googleusercontent.com
GOOGLE_OAUTH2_IOS_CLIENT_ID=************-f8nbj0mqe4lgpl5283pbuoi9p9tju388.apps.googleusercontent.com
APPLE_OAUTH2_MOBILE_CLIENT_ID=org.llif.mylife
APPLE_OAUTH2_WEB_CLIENT_ID=org.llif.mylife.web
FITBIT_OAUTH2_WEB_CLIENT_ID=2386D7
AMAZON_OAUTH2_WEB_CLIENT_ID=amzn1.application-oa2-client.379b6d491a3a46d898bab9ec6fd0b1a7


# ENVIRONMENT
RADIUS=10km
AMBEE_RATELIMIT=10000
WEATHERAPI_RATELIMIT=322000
OPENAQ_RATELIMIT=86000
OPENMETEO_RATELIMIT=10000

# AWS Resources
ENV_LABEL=env
ENVIRONMENT_PREFIX=production_
AWS_ACCOUNT=************
ALEXA_VOICE_LOG_ID=amzn1.ask.skill.73e26fe1-7d39-4a5c-b196-70b4cff00d29
ALEXA_VOICE_LOG_NAME=Best Life

# Topics
TOPIC_ANALYSIS_SCHEDULED=analytic_scheduled
TOPIC_ANALYSIS_FINISHED=analytic_finished
TOPIC_MEMBER_USER_REGISTERED=member_user_registered
TOPIC_TAKEOUT_EXPORT_FINISHED=takeout_export_finished
TOPIC_MEMBER_USER_ACCOUNT_LINKED=member_user_account_linked

# Queues
QUEUE_SINGLE_CORRELATION_JOB=single_correlation_job_queue
QUEUE_TREND_INSIGHTS_JOB=trend_insights_job_queue
QUEUE_NOTIFY_HANDLER_JOB=notify_handler_job_queue

# Sentry
TELEMETRY_TRACES_SAMPLE_RATE=1.0
TELEMETRY_PROFILE_SAMPLE_RATE=1.0
TELEMETRY_REQUEST_BODIES=medium

# UseCases
REMIND_ACTIVITY_LOCAL_HOUR=19
REMIND_PLANS_LOCAL_HOUR=3
DAYS_CONSIDERED_ACTIVE=30

# Extensions
TREND_INSIGHTS_EXTENSION_ID=d26245a6-16fe-4a96-a4dc-bc31d9336e2a
TREND_INSIGHTS_EXTENSION_SCHEDULE=weekly
SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID=f36589a6-16fe-4a96-a4dc-bc31d9336e2a
SINGLE_CORRELATION_EVALUATOR_SCHEDULE=monthly

# Clients
BEST_LIFE_APP_ID=a67e3661-2d53-451b-8095-75589fc0e452

# System
MY_LLIF_FILE_SERVICE_ID=********-4a5e-4583-9a54-3e8ab45e7f5b
MY_LLIF_ID=0099d1b3-dcfd-4d6f-9b60-1986021a8dc4

# Slack Channels
COST_NOTIFICATION_CHANNEL_ID=C024VT390H4

# TEMPORAL
TEMPORAL_HOST=temporal
TEMPORAL_PORT=7233