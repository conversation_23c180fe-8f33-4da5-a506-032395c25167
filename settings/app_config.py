import os
from pathlib import Path
from typing import List, Literal, Optional
from uuid import UUID

from dotenv import load_dotenv
from pydantic import Field, ValidationError, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from services.base.domain.annotated_types import NonEmptyStr
from settings.app_constants import RUN_ENV_LOCAL
from settings.set_logging import set_default_logging

# Main configuration entrypoint - should be used to fetch app settings
RUN_ENV: str = os.getenv("RUN_ENV", RUN_ENV_LOCAL)
ROOT_DIR: Path = Path(__file__).resolve(strict=True).parent.parent
DOT_ENV_FILE_PATH: str = os.path.join(ROOT_DIR, "settings", f".env.{RUN_ENV}")

if not os.path.isfile(DOT_ENV_FILE_PATH):
    raise ValueError(f"Expected configuration file {DOT_ENV_FILE_PATH} not found for running in {RUN_ENV} environment")

# Currently, content of .env file needs to be loaded explicitly
# otherwise Pytest runs fails to load the content in the BaseSettings initialization
# The order matters here! Setting RUN_ENV after loading DOT_ENV would mean any RUN_ENV env var passed in is OVERRIDDEN!
load_dotenv(DOT_ENV_FILE_PATH)


class AppSettings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=DOT_ENV_FILE_PATH,
        env_file_encoding="utf-8",
        extra="ignore",
    )

    @model_validator(mode="after")
    def combine_data_loading_path(self):
        # Validator needs to be used to properly combine fields during class construction
        self.DATA_SOURCE_DIR_PATH = os.path.normpath(os.path.join(self.ROOT_DIR, self.DATA_SOURCE_DIR))
        return self

    # GENERAL
    RUN_ENV: str = RUN_ENV
    APP_VERSION: str = Field(default="unknown")
    BUILD_VERSION: Optional[str] = Field(default=None)
    IS_CONTAINERIZED: bool = Field(default=False)
    ENVIRONMENT_PREFIX: str
    ENV_LABEL: str
    USE_SSL: bool = Field(default=True)
    LOG_LEVEL: str = Field(default="WARNING")
    ROOT_DIR: Path = ROOT_DIR

    # URLS
    AFTER_LOGIN_REDIRECT_URL: str
    AFTER_LOGIN_IOS_REDIRECT_URL: str

    # DATA LOADING
    # default seed data source dir
    DATA_SOURCE_DIR: str = Field(alias="DATA_SOURCE_DIR")
    # full path to the default data source dir
    DATA_SOURCE_DIR_PATH: str = os.path.normpath(os.path.join(ROOT_DIR, str(DATA_SOURCE_DIR)))

    # DATABASE
    SPLIT_INDEX_POSTFIX_FORMAT: str
    SPLIT_INDEX_POSTFIX_TEMPLATE: str

    OS_HOSTS: List[str]
    OS_HOST_LOCAL: Optional[str] = Field(default=None)
    OS_PRIMARY_SHARDS_COUNT: int
    OS_REPLICA_SHARDS_COUNT: int
    OS_MAXIMUM_DOCUMENT_COUNT: int
    OS_MAXIMUM_INDEX_STORAGE: str
    OS_BULK_MAX_LINES_PER_COMMIT: int = Field(default=2000)
    # Defaults to 10 MB which is limit on the opensearch bulk api
    OS_BULK_MAX_SIZE_PER_COMMIT: int = Field(default=10 * (2 << 19))

    PG_NAME: str = Field(alias="POSTGRES_DB")
    PG_HOST: str = Field(alias="POSTGRES_HOST")
    PG_PORT: int = Field(alias="POSTGRES_PORT")

    # AUTH
    API_ACCESS_TOKEN_EXPIRATION_TIME_MINUTES: int
    API_REFRESH_TOKEN_EXPIRATION_TIME_DAYS: int
    API_REFRESH_TOKEN_ROTATION_TIME_DAYS: int

    # AWS
    S3_ROOT: str = Field(default="/s3")

    # PROVIDERS
    GOOGLE_OAUTH2_WEB_CLIENT_ID: str
    GOOGLE_OAUTH2_IOS_CLIENT_ID: str
    APPLE_OAUTH2_MOBILE_CLIENT_ID: str
    APPLE_OAUTH2_WEB_CLIENT_ID: str
    FITBIT_OAUTH2_WEB_CLIENT_ID: str
    AMAZON_OAUTH2_WEB_CLIENT_ID: str

    # Object Storage
    ASSETS_DIRECTORY: str
    EXPORTS_DIRECTORY: str
    ASSET_STORAGE_URL: Optional[str] = None

    AWS_SELF_HOSTED: bool = Field(default=True)
    AWS_URL: Optional[str] = Field(default=None)
    AWS_URL_LOCAL: Optional[str] = Field(default=None)
    AWS_REGION_NAME: str
    AWS_ACCOUNT: str

    # Alexa
    ALEXA_VOICE_LOG_NAME: str

    # Environment
    ENVIRONMENT_VALID_RADIUS: str = Field(alias="RADIUS", default="10km")

    # Telemetry
    TELEMETRY_TRACES_SAMPLE_RATE: float = Field(default=1.0)
    TELEMETRY_PROFILE_SAMPLE_RATE: float = Field(default=1.0)
    TELEMETRY_REQUEST_BODIES: str = Field(default="medium")

    # UseCases
    REMIND_ACTIVITY_LOCAL_HOUR: int
    REMIND_PLANS_LOCAL_HOUR: int
    DAYS_CONSIDERED_ACTIVE: int

    # Extensions
    SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID: UUID
    SINGLE_CORRELATION_EVALUATOR_SCHEDULE: Literal["hourly", "daily", "weekly", "monthly"]
    TREND_INSIGHTS_EXTENSION_SCHEDULE: Literal["hourly", "daily", "weekly", "monthly"]
    TREND_INSIGHTS_EXTENSION_ID: UUID
    # System
    MY_LLIF_FILE_SERVICE_ID: UUID
    MY_LLIF_ID: UUID

    # Clients
    BEST_LIFE_APP_ID: UUID
    # Topics
    TOPIC_ANALYSIS_SCHEDULED: NonEmptyStr
    TOPIC_ANALYSIS_FINISHED: NonEmptyStr
    TOPIC_MEMBER_USER_REGISTERED: NonEmptyStr
    TOPIC_MEMBER_USER_ACCOUNT_LINKED: NonEmptyStr
    TOPIC_TAKEOUT_EXPORT_FINISHED: NonEmptyStr

    # Queues
    QUEUE_SINGLE_CORRELATION_JOB: NonEmptyStr
    QUEUE_TREND_INSIGHTS_JOB: NonEmptyStr
    QUEUE_NOTIFY_HANDLER_JOB: NonEmptyStr

    # AI
    COST_NOTIFICATION_CHANNEL_ID: NonEmptyStr


settings = AppSettings()
print(f"Current environment: {settings.RUN_ENV}")
print(f"Current version: {settings.APP_VERSION}")

# Validation
if settings.RUN_ENV == RUN_ENV_LOCAL:
    if not settings.AWS_URL:
        raise ValidationError(f"missing AWS_URL key for mocked environment: {settings.RUN_ENV}")

if settings.RUN_ENV == RUN_ENV_LOCAL and not settings.IS_CONTAINERIZED:
    settings.OS_HOSTS = [settings.OS_HOST_LOCAL]
    settings.AWS_URL = settings.AWS_URL_LOCAL
    settings.PG_HOST = "localhost"
    settings.ASSET_STORAGE_URL = "http://localhost:10000"

# Logging
set_default_logging(log_level=settings.LOG_LEVEL)

if __name__ == "__main__":
    for key, value in settings.model_dump().items():
        print(f"{key}: {value}")
