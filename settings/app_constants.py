import os
from uuid import UUID

# Environment
RUN_ENV_PRODUCTION = "production"
RUN_ENV_LOCAL = "local"
RUN_ENV_DEV = "dev"
RUN_ENV_STAGING = "staging"

# Seeded users
DEMO1_UUID = UUID("277cd8a7-9355-4a2a-b98a-ef13a980b612")
TEST1_UUID = UUID("f09665c4-ed0a-4d6d-9284-de82477caaae")
TEST2_UUID = UUID("07fc53cb-8fb9-4a39-a555-a0137aea140b")
TEST3_UUID = UUID("872561f4-d154-4d2c-a3ab-4d5cb4197daa")
DEMO1_GOOGLE_ID = "102439416354847823523"
TEST1_GOOGLE_ID = "112002440748265514635"
TEST2_GOOGLE_ID = "101684692664671308345"
TEST3_GOOGLE_ID = "102834849401308436992"

# Currently the same as demo but may not be in the future
SAMPLE_UUID1 = DEMO1_UUID

# Testing constants
PATH_WHICH_DOES_NOT_EXIST = os.path.join("file", "that", "does", "not", "exist")
MESSAGE_UNABLE_TO_LOAD = "Unable to load data from file"
MESSAGE_NO_FILES_FOUND = "No files found in expected folder"
MESSAGE_NO_PERSON_FOUND = "Could not find person folder"
