import os
from dataclasses import dataclass


# TODO: service discovery
@dataclass
class AppRoutes:
    AIR_QUALITY_V2_ROUTE: str = (
        f"http://{'wxcache' if os.getenv('IS_CONTAINERIZED') else 'localhost'}:8006/api/v2/airquality"
    )
    POLLEN_V2_ROUTE: str = f"http://{'wxcache' if os.getenv('IS_CONTAINERIZED') else 'localhost'}:8006/api/v2/pollen"
    WEATHER_V2_ROUTE: str = f"http://{'wxcache' if os.getenv('IS_CONTAINERIZED') else 'localhost'}:8006/api/v2/weather"

    FORECAST_AIR_QUALITY_V2_ROUTE: str = (
        f"http://{'wxcache' if os.getenv('IS_CONTAINERIZED') else 'localhost'}:8006/api/v2/forecast/airquality"
    )
    FORECAST_POLLEN_V2_ROUTE: str = (
        f"http://{'wxcache' if os.getenv('IS_CONTAINERIZED') else 'localhost'}:8006/api/v2/forecast/pollen"
    )
    FORECAST_WEATHER_V2_ROUTE: str = (
        f"http://{'wxcache' if os.getenv('IS_CONTAINERIZED') else 'localhost'}:8006/api/v2/forecast/weather"
    )

    COMPLETE_PLAN_ROUTE: str = (
        f"http://{'data_service' if os.getenv('IS_CONTAINERIZED') else 'localhost'}:8003" + "/v3/plan/complete/"
    )
