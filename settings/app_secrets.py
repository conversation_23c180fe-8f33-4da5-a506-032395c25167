import json
import logging
import os
from typing import Dict, List, Optional

from pydantic import Field, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from services.base.infrastructure.aws.secrets_manager_wrapper import get_secrets
from settings.app_config import RUN_ENV, settings
from settings.app_constants import RUN_ENV_DEV, RUN_ENV_LOCAL, RUN_ENV_PRODUCTION, RUN_ENV_STAGING

_secrets_name: Dict[str, str] = {
    RUN_ENV_PRODUCTION: "prod/myllif",
    RUN_ENV_STAGING: "staging/myllif",
    RUN_ENV_DEV: "dev/myllif",
}


if RUN_ENV != RUN_ENV_LOCAL:
    secret_category: str = _secrets_name[RUN_ENV]
    SECRETS_POSTGRES = f"{secret_category}/postgres"
    SECRETS_GOOGLE = f"{secret_category}/googleoauth"
    SECRETS_FITBIT = f"{secret_category}/fitbitoauth"
    SECRETS_AMAZON = f"{secret_category}/amazonoauth"
    SECRETS_ZOHO = f"{secret_category}/zoho"
    SECRETS_AUTH = f"{secret_category}/auth"
    SECRETS_MAIL_WHITELIST = f"{secret_category}/tester_mail_whitelist"
    SECRETS_ENCRYPTION = f"{secret_category}/encryption"
    SECRETS_FIREBASE = f"{secret_category}/firebaseauth"
    SECRETS_HASH_KEYS = f"{secret_category}/hash_keys"
    SECRETS_STORAGE = f"{secret_category}/storage"
    SECRETS_SENTRY = "myllif/sentry"
    SECRETS_LOGFIRE = "myllif/logfire"
    SECRETS_AI = f"{secret_category}/ai"
    SECRETS_COST_NOTIFICATION_BOT_TOKEN = "myllif/cost_notification_bot_token"
    SECRETS_YOUTUBE = f"{secret_category}/youtube"
    SECRETS_AZURE = f"{secret_category}/azure"
    SECRETS_LOOKUP = f"{secret_category}/lookup"
    secrets_map: dict = get_secrets(
        secret_names=[
            SECRETS_POSTGRES,
            SECRETS_GOOGLE,
            SECRETS_FITBIT,
            SECRETS_AMAZON,
            SECRETS_ZOHO,
            SECRETS_AUTH,
            SECRETS_MAIL_WHITELIST,
            SECRETS_ENCRYPTION,
            SECRETS_FIREBASE,
            SECRETS_HASH_KEYS,
            SECRETS_STORAGE,
            SECRETS_SENTRY,
            SECRETS_LOGFIRE,
            SECRETS_AI,
            SECRETS_COST_NOTIFICATION_BOT_TOKEN,
            SECRETS_YOUTUBE,
            SECRETS_AZURE,
            SECRETS_LOOKUP,
        ]
    )

    # POSTGRES
    os.environ["POSTGRES_USER"] = secrets_map[SECRETS_POSTGRES]["username"]
    os.environ["POSTGRES_PASSWORD"] = secrets_map[SECRETS_POSTGRES]["password"]

    # GOOGLE OAUTH
    os.environ["GOOGLE_OAUTH2_SECRET"] = secrets_map[SECRETS_GOOGLE]["ClientSecret"]
    os.environ["GOOGLE_OAUTH2_CODE_VERIFIER"] = secrets_map[SECRETS_GOOGLE]["ClientCodeVerifier"]
    os.environ["GOOGLE_OAUTH2_CODE_CHALLENGE"] = secrets_map[SECRETS_GOOGLE]["ClientCodeChallenge"]

    # FITBIT OAUTH
    os.environ["FITBIT_OAUTH2_WEB_CLIENT_SECRET"] = secrets_map[SECRETS_FITBIT]["ClientSecret"]
    os.environ["FITBIT_OAUTH2_CODE_VERIFIER"] = secrets_map[SECRETS_FITBIT]["ClientCodeVerifier"]
    os.environ["FITBIT_OAUTH2_CODE_CHALLENGE"] = secrets_map[SECRETS_FITBIT]["ClientCodeChallenge"]

    # AMAZON OAUTH
    os.environ["AMAZON_OAUTH2_WEB_CLIENT_SECRET"] = secrets_map[SECRETS_AMAZON]["ClientSecret"]

    # ZOHO
    os.environ["ZOHO_REFRESH_TOKEN"] = secrets_map[SECRETS_ZOHO]["ZohoRefreshToken"]
    os.environ["ZOHO_CLIENT_ID"] = secrets_map[SECRETS_ZOHO]["ZohoClientId"]
    os.environ["ZOHO_CLIENT_SECRET"] = secrets_map[SECRETS_ZOHO]["ZohoClientSecret"]

    # ACCESS TOKENS
    os.environ["ACCESS_TOKEN_SECRET"] = secrets_map[SECRETS_AUTH]["ACCESS_TOKEN_SECRET"]
    os.environ["REFRESH_TOKEN_SECRET"] = secrets_map[SECRETS_AUTH]["REFRESH_TOKEN_SECRET"]

    # MAIL WHITELIST
    os.environ["LOGIN_MAIL_WHITELIST"] = json.dumps(secrets_map[SECRETS_MAIL_WHITELIST])

    # ENCRYPTION
    os.environ["AUTHORIZATION_ENCRYPTION_KEY"] = secrets_map[SECRETS_ENCRYPTION]["AUTHORIZATION_ENCRYPTION_KEY"]
    os.environ["RECOVERY_ENCRYPTION_KEY"] = secrets_map[SECRETS_ENCRYPTION]["RECOVERY_ENCRYPTION_KEY"]

    # FIREBASE
    os.environ["FIREBASE_CREDENTIALS"] = json.dumps(secrets_map[SECRETS_FIREBASE])

    # HASH KEYS
    os.environ["PUBLIC_UUID_HASH_KEY"] = secrets_map[SECRETS_HASH_KEYS]["PublicUUID"]

    # OBJECT STORAGE
    os.environ["ASSET_STORAGE_CREDENTIALS"] = secrets_map[SECRETS_STORAGE]["AssetStorageCredentials"]

    # LOGFIRE
    os.environ["MY_LLIF_LOGFIRE_WRITE_TOKEN"] = secrets_map[SECRETS_LOGFIRE]["MY_LLIF_WRITE_TOKEN"]

    # SENTRY
    os.environ["MY_LLIF_SENTRY_DSN"] = secrets_map[SECRETS_SENTRY]["MY_LLIF_DSN"]
    os.environ["MY_LLIF_APPS_SENTRY_DSN"] = secrets_map[SECRETS_SENTRY]["MY_LLIF_APPS_DSN"]

    # AI
    os.environ["OPENAI_API_KEY"] = secrets_map[SECRETS_AI]["OPENAI_API_KEY"]
    os.environ["GROQ_API_KEY"] = secrets_map[SECRETS_AI]["GROQ_API_KEY"]

    # Slack Bots
    os.environ["COST_NOTIFICATION_BOT_TOKEN"] = secrets_map[SECRETS_COST_NOTIFICATION_BOT_TOKEN][
        "COST_NOTIFICATION_BOT_TOKEN"
    ]

    # YouTube
    os.environ["YOUTUBE_API_KEY"] = secrets_map[SECRETS_YOUTUBE]["YOUTUBE_API_KEY"]

    # AZURE
    os.environ["AZURE_APP_INSIGHTS_CONNECTION_STRING"] = secrets_map[SECRETS_AZURE]["APP_INSIGHTS_CONNECTION_STRING"]

    # USDA
    os.environ["USDA_API_KEY"] = secrets_map[SECRETS_LOOKUP]["USDA_API_KEY"]


class AppSecrets(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=f".env.{RUN_ENV}",
        env_file_encoding="utf-8",
        extra="allow",
    )

    @model_validator(mode="before")
    def validate_secret_placeholders(cls, values: dict):
        for key, value in values.items():
            if "secret" in str(value):
                logging.error(
                    f"It seems that you are using secret placeholder for {key}. App functionality might be limited."
                )
        return values

    # POSTGRES
    PG_USER: str = Field(alias="POSTGRES_USER")
    PG_PASSWORD: str = Field(alias="POSTGRES_PASSWORD")

    # GOOGLE OAUTH
    GOOGLE_OAUTH2_WEB_CLIENT_SECRET: str = Field(alias="GOOGLE_OAUTH2_SECRET")
    GOOGLE_OAUTH2_CODE_VERIFIER: str = Field(alias="GOOGLE_OAUTH2_CODE_VERIFIER")
    GOOGLE_OAUTH2_CODE_CHALLENGE: str = Field(alias="GOOGLE_OAUTH2_CODE_CHALLENGE")

    # FITBIT OAUTH
    FITBIT_OAUTH2_WEB_CLIENT_SECRET: str = Field(alias="FITBIT_OAUTH2_WEB_CLIENT_SECRET")
    FITBIT_OAUTH2_CODE_VERIFIER: str = Field(alias="FITBIT_OAUTH2_CODE_VERIFIER")
    FITBIT_OAUTH2_CODE_CHALLENGE: str = Field(alias="FITBIT_OAUTH2_CODE_CHALLENGE")

    # AMAZON OAUTH
    AMAZON_OAUTH2_WEB_CLIENT_SECRET: str = Field(alias="AMAZON_OAUTH2_WEB_CLIENT_SECRET")

    # ZOHO
    ZOHO_REFRESH_TOKEN: Optional[str] = Field(alias="ZOHO_REFRESH_TOKEN", default=None)
    ZOHO_CLIENT_ID: Optional[str] = Field(alias="ZOHO_CLIENT_ID", default=None)
    ZOHO_CLIENT_SECRET: Optional[str] = Field(alias="ZOHO_CLIENT_SECRET", default=None)

    # TOKENS
    ACCESS_TOKEN_SECRET: str = Field(alias="ACCESS_TOKEN_SECRET")
    REFRESH_TOKEN_SECRET: str = Field(alias="REFRESH_TOKEN_SECRET")

    # MAIL WHITELIST
    LOGIN_MAIL_WHITELIST: List[str] = Field(alias="LOGIN_MAIL_WHITELIST")

    # ENCRYPTION
    AUTHORIZATION_ENCRYPTION_KEY: str = Field(alias="AUTHORIZATION_ENCRYPTION_KEY")
    RECOVERY_ENCRYPTION_KEY: str = Field(alias="RECOVERY_ENCRYPTION_KEY")

    # FIREBASE
    FIREBASE_CREDENTIALS: dict = Field(alias="FIREBASE_CREDENTIALS")

    # HASH KEYS
    PUBLIC_UUID_HASH_KEY: str = Field(alias="PUBLIC_UUID_HASH_KEY")

    # Object Storage
    ASSET_STORAGE_CREDENTIALS: str = Field(alias="ASSET_STORAGE_CREDENTIALS")

    # Logfire
    MY_LLIF_LOGFIRE_WRITE_TOKEN: str = Field(alias="MY_LLIF_LOGFIRE_WRITE_TOKEN")

    # Sentry
    MY_LLIF_SENTRY_DSN: str = Field(alias="MY_LLIF_SENTRY_DSN")
    MY_LLIF_APPS_SENTRY_DSN: str = Field(alias="MY_LLIF_APPS_SENTRY_DSN")

    # Localstack
    LOCALSTACK_API_KEY: Optional[str] = Field(alias="LOCALSTACK_API_KEY", default=None)

    # AI
    OPENAI_API_KEY: Optional[str] = Field(alias="OPENAI_API_KEY")
    GROQ_API_KEY: Optional[str] = Field(alias="GROQ_API_KEY")
    ORCADB_API_KEY: Optional[str] = Field(alias="ORCADB_API_KEY")

    # Slack
    COST_NOTIFICATION_BOT_TOKEN: Optional[str] = Field(alias="COST_NOTIFICATION_BOT_TOKEN", default=None)

    # YouTube
    YOUTUBE_API_KEY: str | None = Field(alias="YOUTUBE_API_KEY", default=None)

    # AZURE
    AZURE_APP_INSIGHTS_CONNECTION_STRING: str | None = Field(alias="AZURE_APP_INSIGHTS_CONNECTION_STRING", default=None)

    # USDA
    USDA_API_KEY: str = Field(alias="USDA_API_KEY")


secrets = AppSecrets()
if settings.RUN_ENV == RUN_ENV_LOCAL:
    secrets.ASSET_STORAGE_CREDENTIALS = secrets.ASSET_STORAGE_CREDENTIALS % settings.ASSET_STORAGE_URL

if __name__ == "__main__":
    for key, value in secrets.model_dump().items():
        print(f"{key}: {value}")
