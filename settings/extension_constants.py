from uuid import UUID

from settings.app_config import settings

LLIF_EXTENSION_PROVIDER_UUID = UUID("4f0c9c2f-f29e-484c-a438-5adfcff8e873")
TREND_INSIGHTS_EXTENSION_ID = settings.TREND_INSIGHTS_EXTENSION_ID
LABEL_TREND_INSIGHTS = "trend_insights"
TREND_INSIGHTS_EXTENSION_NAME = "Trend Insights"
TREND_INSIGHTS_EXTENSION_DOMAIN = "org.llif.trend_insights"
TREND_INSIGHTS_EXTENSION_DESCRIPTION = (
    "The LLIF Trend Insights Extension identifies common statistics and trends over short, medium, and long term by "
    "looking at the recent week, month, or quarter compared to the previous 3 weeks, 2 months, or 9 months."
    " Shorter term trends require a larger change. Longer term changes may be smaller."
)

LABEL_SINGLE_CORRELATION_EVALUATOR = "single correlation evaluator"
SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID = settings.SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID
SINGLE_CORRELATION_EVALUATOR_EXTENSION_NAME = "Single Correlation Evaluator"
SINGLE_CORRELATION_EVALUATOR_EXTENSION_DOMAIN = "org.llif.single_correlation_evaluator"
SINGLE_CORRELATION_EVALUATOR_EXTENSION_DESCRIPTION = (
    "The LLIF Single Correlation Extension identifies relationships between user data"
    " for user inputted events, that have enough submissions and for biometric data"
    " synced through third party devices. The more data the extension consumes the"
    " more reliable it will be."
)
