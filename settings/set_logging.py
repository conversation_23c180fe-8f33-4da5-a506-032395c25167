import json
import logging


class SerializeFilter(logging.Filter):
    STD_KEYS = {
        "lineno",
        "msg",
        "pathname",
        "msecs",
        "levelname",
        "threadName",
        "name",
        "args",
        "thread",
        "filename",
        "taskName",
        "module",
        "levelno",
        "stack_info",
        "exc_info",
        "relativeCreated",
        "created",
        "funcName",
        "exc_text",
        "process",
        "processName",
    }

    def filter(self, record: logging.LogRecord) -> bool:
        for key in record.__dict__.keys() - self.STD_KEYS:
            val = record.__dict__[key]
            if key in self.STD_KEYS:
                continue
            if isinstance(val, (int, str, bool, bytes, float)):
                continue
            try:
                record.__dict__[key] = json.dumps(val, default=str)
            except Exception as exc:
                logging.error("Cannot serialize extra logging args", extra={"exc": exc, "val": val})
        return True


def set_default_logging(log_level: str = "INFO"):
    logging.basicConfig(
        level=logging.getLevelName(log_level),
        format="[%(levelname).1s] %(asctime)s %(name)s: %(filename)s:%(lineno)d %(message)s",
        datefmt="%Y-%m-%dT%H:%M:%S",
    )

    class StrictLogger(logging.getLoggerClass()):
        def __init__(self, name):
            super().__init__(name)
            if self.level == logging.NOTSET:
                self.setLevel(logging.WARNING)

    # Override default logger class to enforce the stricter default level
    logging.setLoggerClass(StrictLogger)
    logging.getLogger().addFilter(SerializeFilter())

    # Override existing loggers
    for name in logging.root.manager.loggerDict:
        logger = logging.getLogger(name)
        if logger.level == logging.NOTSET:
            logger.setLevel(logging.WARNING)
