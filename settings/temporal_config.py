import os
from pathlib import Path

from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from services.base.domain.annotated_types import NonEmptyStr
from settings.app_constants import RUN_ENV_LOCAL

# Configuration entrypoint for Temporal service
RUN_ENV: str = os.getenv("RUN_ENV", RUN_ENV_LOCAL)
ROOT_DIR: Path = Path(__file__).resolve(strict=True).parent.parent
DOT_ENV_FILE_PATH: str = os.path.join(ROOT_DIR, "settings", f".env.{RUN_ENV}")

if not os.path.isfile(DOT_ENV_FILE_PATH):
    raise ValueError(f"Expected configuration file {DOT_ENV_FILE_PATH} not found for running in {RUN_ENV} environment")

# Currently, content of .env file needs to be loaded explicitly
# otherwise Pytest runs fails to load the content in the BaseSettings initialization
# The order matters here! Setting RUN_ENV after loading DOT_ENV would mean any RUN_ENV env var passed in is OVERRIDDEN!
load_dotenv(DOT_ENV_FILE_PATH)


class TemporalSettings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=DOT_ENV_FILE_PATH,
        env_file_encoding="utf-8",
        extra="ignore",
    )

    RUN_ENV: str = RUN_ENV
    IS_CONTAINERIZED: bool = Field(default=False)
    TEMPORAL_HOST: NonEmptyStr
    TEMPORAL_PORT: int


temporal_settings = TemporalSettings()

if temporal_settings.RUN_ENV == RUN_ENV_LOCAL and not temporal_settings.IS_CONTAINERIZED:
    temporal_settings.TEMPORAL_HOST = "localhost"

if __name__ == "__main__":
    for key, value in temporal_settings.model_dump().items():
        print(f"{key}: {value}")
