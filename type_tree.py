from collections import defaultdict
from functools import lru_cache
from typing import Dict, List, Set, Union, Iterator, Sequence


class TypeTree:
    """
    Build O(1) parent/children indices from a nested structure where
      - internal nodes are dicts
      - leaves can be sets of strings or a single string.
    """

    __slots__ = ('_children', '_parent', '_roots', '_all_nodes')

    def __init__(self, tree_like: Dict[str, Union[Dict, Set[str], str]]) -> None:
        if not isinstance(tree_like, dict):
            raise TypeError("tree_like must be a dictionary")
        if not tree_like:
            raise ValueError("tree_like cannot be empty")

        self._children = defaultdict(set)
        self._parent = {}
        self._roots = set()
        self._all_nodes = set()
        self._build(tree_like)
        self._validate()

    def parent(self, node: str) -> str | None:
        return self._parent.get(node)

    def children(self, node: str) -> Sequence[str]:
        return sorted(self._children.get(node, ()))

    def root(self) -> str | None:
        """Get the single root node, or None if there are multiple roots."""
        return next(iter(self._roots)) if len(self._roots) == 1 else None

    def roots(self) -> Sequence[str]:
        return sorted(self._roots)

    def contains(self, node_or_path: Union[str, List[str]]) -> bool:
        """
        Check if node or path exists in the tree.

        Args:
            node_or_path: Either a node name (str) or a path from root (list[str])
        """
        assert node_or_path
        if isinstance(node_or_path, str):
            return node_or_path in self._all_nodes
        elif isinstance(node_or_path, list):
            for node in node_or_path:
                assert node
                if node not in self._all_nodes:
                    return False

            # Check if the path is actually valid (each node is parent of next)
            for i in range(len(node_or_path) - 1):
                parent = node_or_path[i]
                child = node_or_path[i + 1]
                if child not in self._children.get(parent, set()):
                    return False

            # Check if first node is a root
            return node_or_path[0] in self._roots
        else:
            raise TypeError("node_or_path must be a string or list of strings")

    def is_leaf(self, node: str) -> bool:
        return len(self._children.get(node, ())) == 0

    def is_root(self, node: str) -> bool:
        return node in self._roots

    def depth(self, node: str) -> int | None:
        path = self.path_from_root(node)
        return len(path) - 1 if path else None

    def siblings(self, node: str) -> List[str]:
        parent = self.parent(node)
        if parent is None:
            return []
        return [child for child in self.children(parent) if child != node]

    @lru_cache(maxsize=None)
    def path_from_root(self, node: str) -> List[str] | None:
        if not self.contains(node):
            return None
        path = [node]
        cur = node
        while cur in self._parent:
            cur = self._parent[cur]
            path.append(cur)
        path.reverse()
        return path


    def get_children_paths(self, node: str, leaves_only: bool, from_root: bool = True, max_depth: int | None = None) -> List[List[str]] | None:
        """
        Return list of paths (each a list[str]) to descendants of `node`.

        Args:
          node: start node
          from_root: if True, paths begin at the tree's root (or the top of
                     the forest if multiple roots). If False, paths begin at `node`.
          leaves_only: if True, only include paths that end at leaves
                       (nodes with no children). If False, include paths to all
                       descendants encountered (including internal nodes).
          max_depth: limit on edges from `node` to the end of each path.
                     None means unlimited; 1 means immediate children.

        """
        if not self.contains(node):
            return None

        base_from_root = self.path_from_root(node) if from_root else [node]
        if not self._children.get(node):
            return []

        paths = self.dfs(node, collect_paths=True, max_depth=max_depth, leaves_only=leaves_only)

        if from_root:
            prefix = base_from_root[:-1]  # everything up to (but not including) `node`
            return [prefix + rel for rel in paths]
        else:
            return paths

    def __iter__(self) -> Iterator[str]:
        """Iterate over all nodes in the tree."""
        return iter(self._all_nodes)

    def dfs(self, start_node: str,
            collect_paths: bool = False,
            max_depth: int | None = None,
            leaves_only: bool = False) -> Union[List[List[str]], Iterator[str]]:
        """
        Depth-first search traversal of the tree.

        Args:
            start_node: Node to start traversal from
            collect_paths: If True, collect and return paths instead of nodes
            max_depth: Maximum depth to traverse (None for unlimited)
            leaves_only: If True with collect_paths, only collect paths to leaves

        Returns:
            If collect_paths=True: List of paths (each path is a list of strings)
            If collect_paths=False: Iterator of node names
        """
        if not self.contains(start_node):
            if collect_paths:
                return []
            else:
                return iter([])

        if collect_paths:
            paths = []

            def collect_dfs(cur, path, depth):
                # For leaves_only mode, only add paths that end at leaves
                # For non-leaves_only mode, add all paths except the start node itself
                if cur != start_node:
                    if (not leaves_only) or self.is_leaf(cur):
                        paths.append(path[:])

                # Stop recursion if we've reached max_depth
                if max_depth is not None and depth >= max_depth:
                    return

                # Continue traversing children
                for child in sorted(self.children(cur)):
                    collect_dfs(child, path + [child], depth + 1)

            collect_dfs(start_node, [start_node], 0)
            return paths

        else:
            # Non-path collection mode: return iterator of nodes
            def traverse():
                stack = [(start_node, 0)]  # (node, depth)
                visited = set()

                while stack:
                    node, depth = stack.pop()
                    if node not in visited:
                        visited.add(node)
                        yield node

                        # Add children to stack if we haven't reached max_depth
                        if max_depth is None or depth < max_depth:
                            children = list(reversed(sorted(self.children(node))))
                            for child in children:
                                stack.append((child, depth + 1))

            return traverse()

    def _validate(self) -> None:
        # A cycle exists if we can reach a node through multiple paths
        for root in self._roots:
            visited = set()
            rec_stack = set()

            def has_cycle(node):
                if node in rec_stack:
                    return True
                if node in visited:
                    return False

                visited.add(node)
                rec_stack.add(node)

                children = list(self._children.get(node, set()))
                for child in children:
                    if has_cycle(child):
                        return True

                rec_stack.remove(node)
                return False

            if has_cycle(root):
                raise ValueError(f"Cycle detected in tree starting from root: {root}")

    def _build(self, obj):
        def add_edge(p, c):
            self._children[p].add(c)
            self._parent[c] = p
            self._children.setdefault(c, set())
            self._roots.discard(c)
            self._all_nodes.add(p)
            self._all_nodes.add(c)

        def ensure_root(n):
            self._children.setdefault(n, set())
            self._all_nodes.add(n)
            if n not in self._parent:
                self._roots.add(n)

        def walk(o, p=None):
            if isinstance(o, dict):
                for k, v in o.items():
                    if p is None:
                        ensure_root(k)
                        walk(v, k)
                    else:
                        add_edge(p, k)
                        walk(v, k)
            elif isinstance(o, set):
                for leaf in o:
                    add_edge(p, leaf)
                    self._children.setdefault(leaf, set())
                    self._all_nodes.add(leaf)
            elif isinstance(o, str):
                # Only add edge if it's not a self-reference
                if p != o:
                    add_edge(p, o)
                    self._children.setdefault(o, set())
                    self._all_nodes.add(o)
                else:
                    # Self-reference means this is just a leaf marker
                    self._children.setdefault(p, set())
                    self._all_nodes.add(p)
            else:
                raise TypeError(f"Unsupported type: {type(o)}")

        walk(obj)

if __name__ == '__main__':
    t = TypeTree(
        tree_like={
            "event": {
                "nutrition": {
                    "food": {
                        "meat": {"beef", "pork", "chicken"},
                        "drink": {"water", "juice", "milk"},
                    },
                },
                "exercise": {"cardio": {"running", "cycling"}, "strength": {"weights", "yoga"}},
                "content": {"video", "audio"},
            }
        }
    )
    print(list(t.dfs(start_node="event")))