import pytest

from type_tree import TypeTree


class TestTypeTree:
    """Test suite for TypeTree class."""

    SAMPLE_TREE = TypeTree({
            "event": {
                "nutrition": {
                    "food": {
                        "meat": {"beef", "pork", "chicken"},
                        "drink": {"water", "juice", "milk"},
                    },
                },
                "exercise": {"cardio": {"running", "cycling"}, "strength": {"weights", "yoga"}},
                "content": {"video", "audio"},
            }
        })


if __name__ == "__main__":
    T = TestTypeTree().SAMPLE_TREE

    print("\n=== Original Functionality ===")
    print(f"Parent of 'cycling': {T.parent('cycling')}")  # 'cardio'
    print(f"Children of 'food': {T.children('food')}")  # ['drink', 'meat']
    print(f"Path from root to 'juice': {T.path_from_root('juice')}")
    print(f"Root: {T.root()}")  # 'event'

    print("\n=== New Utility Methods ===")
    print(f"Contains 'cycling': {T.contains('cycling')}")  # True
    print(f"Contains 'nonexistent': {T.contains('nonexistent')}")  # False
    print(f"Contains path ['event', 'nutrition', 'food']: {T.contains(['event', 'nutrition', 'food'])}")  # True
    print(f"Contains invalid path ['event', 'invalid', 'path']: {T.contains(['event', 'invalid', 'path'])}")  # False
    print(f"Contains partial path ['nutrition', 'food']: {T.contains(['nutrition', 'food'])}")  # False (not from root)
    print(f"Is 'beef' a leaf: {T.is_leaf('beef')}")  # True
    print(f"Is 'food' a leaf: {T.is_leaf('food')}")  # False
    print(f"Is 'event' a root: {T.is_root('event')}")  # True
    print(f"Depth of 'juice': {T.depth('juice')}")  # 4
    print(f"Siblings of 'running': {T.siblings('running')}")  # ['cycling']

    print("\n=== Iterator Support ===")
    print(f"All nodes: {sorted(list(T))}")

